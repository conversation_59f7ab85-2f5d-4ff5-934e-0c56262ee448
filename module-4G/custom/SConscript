# -*- coding: utf-8 -*-
#====================================================================
#
# Copyright © 2023 China Mobile IOT. All rights reserved.
#
#
#====================================================================


Import('env')
from ModuleBuild import *

module = 'custom_main'  # 模块名，同簇模块不得重名
current_dir = os.path.join(Dir('.').abspath)
target = None


#========================================================
# ram_source_files：运行在ram中
# flash_source_files：运行在flash中
#========================================================
ram_source_files = [
    'user_main/src/main.c',
    'user_main/src/dtu_config.c',
    'user_main/src/dtu_channel.c',
    'user_main/src/dtu_channel_socket.c',
    'user_main/src/dtu_channel_mqtt.c',
    'user_main/src/dtu_channel_http.c',
    'user_main/src/config_uart.c',
    'user_main/src/my_util.c',
    'user_main/src/pika_config.c',
]

flash_source_files = [
]


#========================================================
# public_incs：置于工程环境中，供其他模块引用
# private_incs：仅在本模块中引用
#========================================================
public_incs = [
    current_dir + '/../src/modem',
    current_dir + '/../third-party/cJSONFiles/cJSON',    
    current_dir + '/../third-party/mbedtls/include',
    current_dir + '/../third-party/mbedtls/include/mbedtls',
    current_dir + '/../third-party/pikascript/pikascript-api',
    current_dir + '/../third-party/pikascript/pikascript-core',
]

private_incs = [
    current_dir + '/user_main/inc',
]


#========================================================
# cpp_define：仅适用于本模块，对其他模块不可见
#========================================================
cpp_defines = [
]


#========================================================
# 模块编译
#========================================================
mod = ModuleBuild(
    env,
    name = module,
    ram_srcs = ram_source_files,
    flash_srcs = flash_source_files,
    pub_incs = public_incs,
    pri_incs = private_incs,
    cpp_define = cpp_defines
)

target = mod.build_object()

Return('target')
