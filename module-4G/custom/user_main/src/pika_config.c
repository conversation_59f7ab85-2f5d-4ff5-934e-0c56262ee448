#include "cm_mem.h"
#include "cm_os.h"
#include "stdarg.h"
#include "my_util.h"
#include "stdio.h"

/***************************************************
 * 替换pikascript的内存管理函数
 ***************************************************/

void* pika_platform_malloc(size_t size)
{
    return cm_malloc(size);
}

void* pika_platform_realloc(void* ptr, size_t size)
{
    return cm_realloc(ptr, size);
}

void pika_platform_free(void* ptr)
{
    cm_free(ptr);
}

// pikascript毫秒转tick
size_t pika_platform_tick_from_millisecond(size_t ms) {
    return ms / 5;
}

// pikascript毫秒级延时
void pika_platform_sleep_ms(uint32_t ms) {
    osDelay(pika_platform_tick_from_millisecond(ms));
}

// pikascript打印函数
void pika_platform_printf(char* str, ...) {
    char s[600] = { 0 };
    va_list args;

    if ((str == NULL) || (strlen(str) == 0))
    {
        return;
    }

    va_start(args, str);
    vsnprintf((char*)s, 600, str, args);
    va_end(args);
    console_log(s);
}