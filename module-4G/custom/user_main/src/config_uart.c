#include "config_uart.h"
#include "common.h"
#include "my_util.h"
#include "cm_uart.h"
#include "cm_sys.h"
#include "stdio.h"
#include "stdlib.h"
#include "stdarg.h"
#include "cm_os.h"
#include "cm_mem.h"
#include "cm_virt_at.h"
#include "cm_iomux.h"

// 函数向前声明
static void uart_event_callback(void* param, uint32_t type);
static void uart_read_thread(void* params);
static void uart_pack_timer(void* params);
static void uart_send_msg_to_consume_task(uart_ctx_t* ctx, uint8_t* data, uint32_t len);
static void uart_consume_thread(void* params);
static void uart_auto_pull_thread(void* params);
static void set_virt_at_log2cat(uint32_t uart_num);
void set_uart_iomux_pin_func(uint8_t dev_num);

// 消费任务列表
dynamic_list_t* uart_consume_task_list = NULL;
// 串口上下文列表
dynamic_list_t* uart_ctx_list = NULL;

/**
 * @brief 串口初始化
 * @param[in] config 配置信息
 * @param[out] ctx 串口上下文
 * @return 串口上下文，NULL则为初始化失败
 */
uart_ctx_t* uart_init(dtu_uart_t* config)
{
    uint8_t dev_num = config->dev_num;
    console_log("[uart %d] init", dev_num);

    if (dev_num >= CM_UART_DEV_NUM)
    {
        console_log("[uart %d] init dev_num not valid", dev_num);
        return NULL;
    }

    // 如果之前已经初始化，则先释放
    if (uart_ctx_list != NULL)
    {
        for (uint32_t i = 0; i < uart_ctx_list->size; i++)
        {
            uart_ctx_t* item = (uart_ctx_t*)dynamic_list_get(uart_ctx_list, i);
            if (item->dev_num == dev_num)
            {
                uart_deinit(dev_num);
                break;
            }
        }
    }

    // 申请内存
    uart_ctx_t* ctx = (uart_ctx_t*)cm_malloc(sizeof(uart_ctx_t));
    if (ctx == NULL)
    {
        console_log("[uart %d] init end, malloc ctx fail", dev_num);
        return NULL;
    }

    ctx->config = config;
    ctx->dev_num = dev_num;

    // 读取线程信号量
    ctx->read_sem = osSemaphoreNew(1, 0, NULL);

    // 创建读取线程
    osThreadAttr_t receive_thread_attr = { 0 };
    char receive_thread_name[30] = { 0 };
    sprintf(receive_thread_name, "uart %d receive thread", dev_num);
    receive_thread_attr.name = receive_thread_name;
    receive_thread_attr.stack_size = 2048;
    receive_thread_attr.priority = osPriorityNormal;
    ctx->read_thread_id = osThreadNew(uart_read_thread, ctx, &receive_thread_attr);

    // 分包定时器
    ctx->receive_pack_timer_id = osTimerNew(uart_pack_timer, osTimerOnce, ctx, NULL);

    // 自动拉取线程
    if (config->pull_interval > 0 && config->cmd_cnt > 0)
    {
        osThreadAttr_t auto_pull_thread_attr = { 0 };
        char auto_pull_thread_name[30] = { 0 };
        sprintf(auto_pull_thread_name, "uart %d auto pull thread", dev_num);
        auto_pull_thread_attr.name = auto_pull_thread_name;
        auto_pull_thread_attr.stack_size = 2048;
        auto_pull_thread_attr.priority = osPriorityNormal;
        ctx->read_thread_id = osThreadNew(uart_auto_pull_thread, ctx, &auto_pull_thread_attr);
    }

    /* 若为UART2，需要先将log打印从debug切换到usb */
    set_virt_at_log2cat(dev_num);

    /* 配置引脚复用 */
    set_uart_iomux_pin_func(dev_num);

    /* 配置串口唤醒，只有UART0具有串口唤醒功能 */
    if (dev_num == CM_UART_DEV_0)
    {
        /* 配置uart唤醒功能，使能边沿检测才具备唤醒功能，仅主串口具有唤醒功能，用于唤醒的数据并不能被uart接收，请在唤醒后再进行uart数传 */
        cm_iomux_set_pin_cmd(UART_WEKEUP_PIN, CM_IOMUX_PINCMD1_LPMEDEG, CM_IOMUX_PINCMD1_FUNC1_LPM_EDGE_RISE);
    }

    // 上下文加入列表
    if (uart_ctx_list == NULL)
        uart_ctx_list = dynamic_list_init(sizeof(uart_ctx_t));
    dynamic_list_add(uart_ctx_list, ctx);

    /* 注册事件和回调函数 */
    cm_uart_event_t event = { CM_UART_EVENT_TYPE_RX_ARRIVED, ctx, uart_event_callback };
    int32_t res = cm_uart_register_event(dev_num, &event);
    if (res != RES_SUCCESS)
    {
        console_log("[uart %d] register event err, res = %d", dev_num, res);
        uart_deinit(dev_num);
        return NULL;
    }

    /* 开启串口 */
    cm_uart_cfg_t uart_config = {
        .baudrate = config->baudrate,
        .byte_size = config->byte_size,
        .stop_bit = config->stop_bit,
        .parity = config->parity,
        .flow_ctrl = CM_UART_FLOW_CTRL_NONE,
        .is_lpuart = 0,
        .rxrb_buf_size = 0,
        .fc_high_threshold = 0,
        .fc_low_threshold = 0
    };
    res = cm_uart_open(dev_num, &uart_config);
    if (res != RES_SUCCESS)
    {
        console_log("[uart %d] open err, res = %d", dev_num, res);
        uart_deinit(dev_num);
        return NULL;
    }

    console_log("[uart %d] init success", dev_num);

    return ctx;
}

/**
 * @brief 串口释放
 * @param[in] dev_num 串口设备编号
 */
void uart_deinit(uint8_t dev_num)
{
    cm_uart_close(dev_num);

    if (uart_ctx_list != NULL)
    {
        for (int i = 0; i < uart_ctx_list->size; i++)
        {
            uart_ctx_t* item = (uart_ctx_t*)dynamic_list_get(uart_ctx_list, i);
            if (item->dev_num == dev_num)
            {
                if (item->auto_pull_thread_id != NULL)
                {
                    osThreadTerminate(item->auto_pull_thread_id);
                    item->auto_pull_thread_id = NULL;
                }

                if (item->receive_pack_timer_id != NULL)
                {
                    osTimerStop(item->receive_pack_timer_id);
                    osTimerDelete(item->receive_pack_timer_id);
                    item->receive_pack_timer_id = NULL;
                }

                if (item->read_thread_id != NULL)
                {
                    osThreadTerminate(item->read_thread_id);
                    item->read_thread_id = NULL;
                }

                if (item->read_sem != NULL)
                {
                    osSemaphoreDelete(item->read_sem);
                    item->read_sem = NULL;
                }

                if (item->receive_buf_len > 0)
                {
                    cm_free(item->receive_buf);
                    item->receive_buf = NULL;
                    item->receive_buf_len = 0;
                }

                item->config = NULL;

                dynamic_list_remove(uart_ctx_list, item);
                i--;

                cm_free(item);
            }
        }
    }
}

/**
 * @brief 注册串口消费任务，同一个函数不会重复注册
 * @param[in] callback 回调函数
 * @param[in] name 任务名称
 * @param[in] stack_size 栈空间大小
 * @return 注册结果
 */
COMMON_RES_T uart_register_consume_task(uart_consume_callback_t callback, char* name, uint32_t stack_size)
{
    console_log("[uart x] register consume task start, task name: %s", name);

    if (callback == NULL || name == NULL || stack_size == 0)
    {
        console_log("[uart x] register consume task failed, invalid param");
        return RES_ERROR;
    }

    if (uart_consume_task_list == NULL)
        uart_consume_task_list = dynamic_list_init(sizeof(uart_consume_task_t));

    // 检查是否重复注册
    for (uint32_t i = 0; i < uart_consume_task_list->size; i++)
    {
        uart_consume_task_t* task = dynamic_list_get(uart_consume_task_list, i);
        if (task->callback == callback)
        {
            console_log("[uart x] register consume task failed, callback already registered");
            return RES_ERROR;
        }
    }

    // 申请内存
    uart_consume_task_t* task = (uart_consume_task_t*)cm_malloc(sizeof(uart_consume_task_t));
    if (task == NULL)
    {
        console_log("[uart x] register consume task failed, malloc failed");
        return RES_ERROR;
    }

    // 创建队列
    osMessageQueueId_t msg_queue_id = osMessageQueueNew(10, sizeof(uart_consume_task_msg_t*), NULL);

    // 赋值
    task->msg_queue_id = msg_queue_id;
    task->callback = callback;
    task->name = name;

    // 创建线程
    osThreadAttr_t consume_task_attr = { 0 };
    consume_task_attr.name = name;
    consume_task_attr.stack_size = stack_size;
    consume_task_attr.priority = osPriorityNormal;
    task->thread_id = osThreadNew(uart_consume_thread, task, &consume_task_attr);

    // 添加到任务列表
    dynamic_list_add(uart_consume_task_list, task);

    console_log("[uart x] register consume task success");

    return RES_SUCCESS;
}

/**
 * @brief 数据串口事件回调
 * 回调函数中不可输出LOG、串口打印、执行复杂任务或消耗过多资源
 * 建议以信号量或消息队列形式控制其他线程执行任务
 *
 * @param[in] param 参数(试过，没用)
 * @param[in] type 类型
 */
static void uart_event_callback(void* param, uint32_t type)
{
    uart_ctx_t* ctx = (uart_ctx_t*)param;
    console_log("uart_event_callback dev_num:%d type:%d", ctx->dev_num, type);
    if (CM_UART_EVENT_TYPE_RX_ARRIVED & type)
    {
        /* 收到接收事件，触发其他线程执行读取数据 */
        osSemaphoreRelease(ctx->read_sem);
    }
}

/**
 * @brief 串口接收任务处理
 * @param[in] params 参数
 */
static void uart_read_thread(void* param)
{
    uart_ctx_t* ctx = (uart_ctx_t*)param;
    dtu_uart_t config = *(ctx->config);
    uint8_t dev_num = ctx->dev_num;
    console_log("uart_read_thread dev_num:%d start", dev_num);
    while (TRUE)
    {
        // 等待触发事件
        osSemaphoreAcquire(ctx->read_sem, osWaitForever);

        console_log("[uart %d] receive start", dev_num);

        // 读取串口缓存区数据长度
        uint32_t data_len = cm_uart_get_rxrb_data_len(dev_num);

        // 获取对应串口的缓存信息
        char* current_buffer = ctx->receive_buf;
        uint32_t current_len = ctx->receive_buf_len;

        // 尝试重新分配内存
        char* new_buffer = (char*)cm_realloc(current_buffer, current_len + data_len + 1);
        if (new_buffer == NULL)
        {
            console_log("[uart %d] receive realloc failed", dev_num);
            cm_free(current_buffer);
            ctx->receive_buf = NULL;
            ctx->receive_buf_len = 0;
            continue;;
        }
        current_buffer = new_buffer;

        // 读取串口缓存区数据，并清空串口缓存区
        data_len = cm_uart_read(dev_num, current_buffer + current_len, data_len, 1000);
        cm_uart_clean(dev_num);

        //更新变量信息
        current_len += data_len;
        current_buffer[current_len] = '\0';

        // 如果配置了数据包结束符，则使用结束符分包
        if (config.pack_end_str != NULL && config.pack_end_str[0] != '\0')
        {
            char* found;
            int end_str_len = strlen(config.pack_end_str);
            // 使用结束符进行分包，放入缓存
            while (current_len > 0 && (found = strstr(current_buffer, config.pack_end_str)))
            {
                // 复制分包数据
                int send_buffer_len = found - current_buffer + end_str_len;
                char* send_buffer = copy_str_by_len(current_buffer, send_buffer_len);
                if (send_buffer == NULL)
                {
                    console_log("[uart %d] receive pack by end str malloc send buffer failed", dev_num);
                    cm_free(current_buffer);
                    ctx->receive_buf = NULL;
                    ctx->receive_buf_len = 0;
                    break;
                }

                // 发送分包
                console_log("[uart %d] send pack, len=%d", dev_num, send_buffer_len);
                uart_send_msg_to_consume_task(ctx, (uint8_t*)send_buffer, send_buffer_len);

                // 剩余包
                char* rest_buffer = NULL;
                int rest_buffer_len = current_len - send_buffer_len;
                if (rest_buffer_len > 0)
                {
                    rest_buffer = copy_str_by_len(current_buffer + send_buffer_len, rest_buffer_len);
                    if (rest_buffer == NULL)
                    {
                        console_log("[uart %d] receive pack by end str malloc rest buffer failed", dev_num);
                        cm_free(current_buffer);
                        ctx->receive_buf = NULL;
                        ctx->receive_buf_len = 0;
                        break;
                    }
                }

                cm_free(current_buffer);

                current_buffer = rest_buffer;
                current_len = rest_buffer_len;
            }
        }
        // 如果配置了数据包最大长度，则按照最大长度进行分包
        else if (config.pack_max_len > 1)
        {
            while (current_len > config.pack_max_len)
            {
                // 复制分包数据
                int send_buffer_len = config.pack_max_len;
                char* send_buffer = copy_str_by_len(current_buffer, send_buffer_len);
                if (send_buffer == NULL)
                {
                    console_log("[uart %d] receive pack by max len malloc send buffer failed", dev_num);
                    cm_free(current_buffer);
                    ctx->receive_buf = NULL;
                    ctx->receive_buf_len = 0;
                    break;
                }

                // 发送分包
                console_log("[uart %d] send pack, len=%d", dev_num, send_buffer_len);
                uart_send_msg_to_consume_task(ctx, (uint8_t*)send_buffer, send_buffer_len);

                // 剩余包
                char* rest_buffer = NULL;
                int rest_buffer_len = current_len - send_buffer_len;
                if (rest_buffer_len > 0)
                {
                    rest_buffer = copy_str_by_len(current_buffer + send_buffer_len, rest_buffer_len);
                    if (rest_buffer == NULL)
                    {
                        console_log("[uart %d] receive pack by max len malloc rest buffer failed", dev_num);
                        cm_free(current_buffer);
                        ctx->receive_buf = NULL;
                        ctx->receive_buf_len = 0;
                        break;
                    }
                }

                cm_free(current_buffer);

                current_buffer = rest_buffer;
                current_len = rest_buffer_len;
            }
        }
        else
        {
            // 启动定时器
            osTimerStart(ctx->receive_pack_timer_id, get_ms(config.pack_timeout));
        }

        // 保存当前数据
        ctx->receive_buf = current_buffer;
        ctx->receive_buf_len = current_len;

        console_log("[uart %d] receive end, current_len=%d", dev_num, current_len);
    }
}

/**
 * @brief 串口分包超时处理
 */
static void uart_pack_timer(void* params)
{
    uart_ctx_t* ctx = (uart_ctx_t*)params;
    if (ctx->receive_buf_len > 0)
    {
        uart_send_msg_to_consume_task(ctx, (uint8_t*)ctx->receive_buf, ctx->receive_buf_len);
        ctx->receive_buf = NULL;
        ctx->receive_buf_len = 0;
    }
}

/**
 * @brief 串口数据接收完成，发送给消费任务
 *
 * @param ctx 串口上下文
 * @param data 数据
 * @param len 数据大小
 */
static void uart_send_msg_to_consume_task(uart_ctx_t* ctx, uint8_t* data, uint32_t len)
{
    console_log("[uart %d] send msg to consume task, len=%d", ctx->dev_num, len);

    if (uart_consume_task_list != NULL)
    {
        gc_data_t* gc_data = gc_data_init(data, uart_consume_task_list->size);
        if (gc_data == NULL)
        {
            console_log("[uart %d] send msg to consume task, malloc gc_data failed", ctx->dev_num);
            return;
        }

        for (uint32_t i = 0; i < uart_consume_task_list->size; i++)
        {
            uart_consume_task_msg_t* msg = (uart_consume_task_msg_t*)cm_malloc(sizeof(uart_consume_task_msg_t));
            if (msg == NULL)
            {
                console_log("[uart %d] send msg to consume task, malloc msg failed", ctx->dev_num);
                continue;
            }

            msg->ctx = ctx;
            msg->data = gc_data;
            msg->len = len;

            // 消息放入队列
            uart_consume_task_t* task = (uart_consume_task_t*)dynamic_list_get(uart_consume_task_list, i);
            osMessageQueueId_t msg_queue_id = task->msg_queue_id;
            osMessageQueuePut(msg_queue_id, &msg, 0, 0);
        }
    }

    console_log("[uart %d] end msg to consume task end", ctx->dev_num);
}

/**
 * @brief 串口消费处理任务
 */
static void uart_consume_thread(void* params)
{
    uart_consume_task_t* task = (uart_consume_task_t*)params;

    console_log("[uart x] consume task [%s] start", task->name);

    while (TRUE)
    {
        uart_consume_task_msg_t* msg;
        if (osMessageQueueGet(task->msg_queue_id, &msg, NULL, osWaitForever) == osOK)
        {
            console_log("[uart x] consume task [%s] receive msg start", task->name);

            // 调用回调
            uart_consume_callback_t callback = task->callback;
            gc_data_t* gc_data = msg->data;
            callback(msg->ctx, (uint8_t*)gc_data->data, msg->len);

            // 释放内存
            gc_try_free(gc_data);
            cm_free(msg);

            console_log("[uart x] consume task [%s] receive msg end", task->name);
        }
    }
}

/**
 * @brief 串口定时拉取任务处理
 */
static void uart_auto_pull_thread(void* params)
{
    uart_ctx_t* ctx = (uart_ctx_t*)params;

    console_log("[uart %d] auto pull thread start", ctx->dev_num);

    dtu_uart_t* config = ctx->config;
    while (TRUE)
    {
        console_log("[uart %d] auto pull start", ctx->dev_num);

        for (uint8_t i = 0; i < config->cmd_cnt; i++)
        {
            dtu_cmd_t* cmd = config->cmd_list[i];
            char* cmd_str = cmd->cmd;

            if (cmd_str == NULL || cmd_str[0] == '\0')
                continue;

            console_log("[uart %d] auto pull, cmd=%s", config->dev_num, cmd_str);

            size_t send_data_len = strlen(cmd_str);
            uint8_t* send_data = (uint8_t*)cm_malloc(send_data_len);
            if (send_data == NULL)
            {
                console_log("[uart %d] auto pull, malloc send data failed", config->dev_num);
                continue;
            }
            size_t len = hex_to_bytes((uint8_t*)cmd_str, send_data, send_data_len, cmd->crc);
            if (len > 0)
            {
                cm_uart_write(config->dev_num, send_data, len, 0);
                cm_free(send_data);
                osDelay(get_ms(50));
            }
        }

        console_log("[uart %d] auto pull end", ctx->dev_num);

        osDelay(get_ms(config->pull_interval));
    }
}

/**
 * @brief 设置串口引脚复用功能为串口
 */
void set_uart_iomux_pin_func(uint8_t dev_num)
{

    if (dev_num == 0)
    {
        cm_iomux_set_pin_func(CM_IOMUX_PIN_18, CM_IOMUX_FUNC_FUNCTION1);
        cm_iomux_set_pin_func(CM_IOMUX_PIN_17, CM_IOMUX_FUNC_FUNCTION1);
    }

    if (dev_num == 1)
    {
        cm_iomux_set_pin_func(CM_IOMUX_PIN_28, CM_IOMUX_FUNC_FUNCTION1);
        cm_iomux_set_pin_func(CM_IOMUX_PIN_29, CM_IOMUX_FUNC_FUNCTION1);
    }

    if (dev_num == 2)
    {
        cm_iomux_set_pin_func(CM_IOMUX_PIN_38, CM_IOMUX_FUNC_FUNCTION2);
        cm_iomux_set_pin_func(CM_IOMUX_PIN_39, CM_IOMUX_FUNC_FUNCTION2);
    }
}

/**
 * @brief 如果串口编号是debug串口，则切换debug引脚打印的log到usb打印，掉电不保存配置，其他串口不做处理
 * @param[in] uart_num 串口编号
 */
static void set_virt_at_log2cat(uint32_t uart_num)
{
    if (uart_num != CM_UART_DEV_2)
    {
        return (void)0;
    }

    char operation[64] = { 0 };
    sprintf(operation, "%s\r\n", "AT+MCFG=log2cat,1");
    uint8_t rsp[128] = { 0 };
    int32_t rsp_len = 0;

    if (cm_virt_at_send_sync((const uint8_t*)operation, rsp, &rsp_len, 10) == 0)
    {
        cm_log_printf(0, "rsp=%s rsp_len=%d\n", rsp, rsp_len);
    }
    else
    {
        cm_log_printf(0, "ret != 0\n");
    }
}