#include "main.h"
#include "config_uart.h"
#include "dtu_config.h"
#include "dtu_channel.h"
#include "my_util.h"
#include "cm_os.h"
#include "cm_sys.h"
#include "cm_uart.h"
#include "cm_mem.h"
#include "cm_fs.h"
#include "cm_modem.h"
#include "cm_rtc.h"
#include "cm_eloop.h"
#include "cm_asocket.h"
#include "pikaScript.h"
#include "PikaVM.h"

typedef struct
{
    gc_data_t* data;
    uint32_t len;
    uint8_t dev_num;
} uart_send_t;

// 函数向前声明
void main_task_func(void* param);
void flip_net_led(void* param);
static void handle_uart_receive(uart_ctx_t* ctx, uint8_t* data, uint32_t len);
static void handle_channel_receive(dtu_channel_info_t* channel_info, gc_data_t* data, size_t len);
static void handle_uart_send(void* param);

// 主线程句柄
static osThreadId_t main_task_id = NULL;
// 通道列表
dynamic_list_t* dtu_channel_list = NULL;
// 串口发送队列
osMessageQueueId_t uart_send_queue = NULL;
// 串口发送线程
static osThreadId_t uart_send_thread_id = NULL;
// 网络灯状态
cm_gpio_level_e net_led_level = CM_GPIO_LEVEL_LOW;
// 网络灯闪烁定时器
static osTimerId_t net_led_timer = NULL;


/**
 * @brief 用户程序入口
 */
int cm_opencpu_entry(void* param)
{
    // 打开工作灯
    cm_gpio_cfg_t gpio_cfg = {
        .direction = CM_GPIO_DIRECTION_OUTPUT,
        .pull = CM_GPIO_PULL_NONE,
    };
    cm_iomux_set_pin_func(WORK_LED_PIN, CM_IOMUX_FUNC_FUNCTION1);
    cm_gpio_init(WORK_LED_GPIO, &gpio_cfg);
    cm_gpio_set_level(WORK_LED_GPIO, CM_GPIO_LEVEL_HIGH);

    // 初始化网络灯
    cm_gpio_cfg_t net_gpio_cfg = {
        .direction = CM_GPIO_DIRECTION_OUTPUT,
        .pull = CM_GPIO_PULL_NONE,
    };
    cm_iomux_set_pin_func(NET_LED_PIN, CM_IOMUX_FUNC_FUNCTION1);
    cm_gpio_init(NET_LED_GPIO, &net_gpio_cfg);
    net_led_timer = osTimerNew(flip_net_led, osTimerPeriodic, NULL, NULL);

    // 打印版本信息
    console_log("======= FENYDATA OPENCPU 1.0 =======");

    // SDK版本
    char buff[CM_VER_LEN] = { 0 };
    cm_sys_get_cm_ver(buff, CM_VER_LEN);
    console_log("SDK VERSION: %s", buff);

    // 文件系统占用情况
    cm_fs_system_info_t fs_status = { 0, 0 };
    cm_fs_getinfo(&fs_status);
    console_log("fs status: total:%d, free:%d", fs_status.total_size, fs_status.free_size);

    // 内存占用情况
    cm_heap_stats_t heap_status = { 0 };
    cm_mem_get_heap_stats(&heap_status);
    console_log("heap status: total:%d, free:%d", heap_status.total_size, heap_status.free);

    // 注册串口消费任务
    uart_register_consume_task(handle_uart_receive, "uart consume task", 8192);

    // 串口发送队列
    uart_send_queue = osMessageQueueNew(10, sizeof(uart_send_t*), NULL);

    // 串口发送线程
    osThreadAttr_t recv_task_attr = { 0 };
    recv_task_attr.name = "channel send thread";
    recv_task_attr.stack_size = 1024;
    recv_task_attr.priority = osPriorityNormal;
    uart_send_thread_id = osThreadNew((osThreadFunc_t)handle_uart_send, 0, &recv_task_attr);

    // 初始化gc线程
    gc_task_init();

    // 开启主线任务
    osThreadAttr_t app_task_attr = { 0 };
    app_task_attr.name = "main task";
    app_task_attr.stack_size = 4096 * 2;
    app_task_attr.priority = osPriorityNormal;
    main_task_id = osThreadNew((osThreadFunc_t)main_task_func, 0, &app_task_attr);

    // 主循环，如果没有这个，所有地方的timer都失效
    while (TRUE);

    return 0;
}

// 主线任务
void main_task_func(void* param)
{
    // 获取网络状态
    console_log("[main] waiting for network...");
    int try_times = 0;
    while (TRUE)
    {
        if (try_times > 10)
        {
            console_log("[main] network timeout");
            return;
        }
        if (cm_modem_get_pdp_state(1) == 1)
        {
            console_log("[main] network ready");
            break;
        }
        osDelay(200);
        try_times++;
    }

    // 启动网络灯闪烁
    osTimerStart(net_led_timer, get_ms(500));

    // 拉取配置
    console_log("[main] get dtu config start...");
    dtu_config_t dtu_config = { 0 };
    COMMON_RES_T res = get_dtu_config(&dtu_config);
    if (res != RES_SUCCESS)
    {
        console_log("[main] get cloud config failed");
        return;
    }
    console_log("[main] get dtu config success, version=%s", dtu_config.version);

    // 设置串口采集
    if (dtu_config.uart_cnt > 0)
    {
        for (int i = 0; i < dtu_config.uart_cnt; i++)
        {
            dtu_uart_t* uart = dtu_config.uart_list[i];
            if (uart->enabled)
            {
                uart_init(uart);
            }
        }
    }

    // 设置通道
    if (dtu_config.channel_cnt > 0)
    {
        // 关闭所有通道
        dtu_channel_close_all();

        // 清空绑定关系
        if (dtu_channel_list != NULL)
            dynamic_list_free(dtu_channel_list);

        dtu_channel_list = dynamic_list_init(sizeof(dtu_channel_info_t));

        // 注册通道接收回调
        dtu_channel_register_callback(handle_channel_receive);

        for (int i = 0; i < dtu_config.channel_cnt; i++)
        {
            dtu_channel_t* channel = dtu_config.channel_list[i];
            if (!channel->enabled)
                continue;

            // 注册通道
            dtu_channel_info_t* channel_info = dtu_channel_init(channel);
            if (channel_info == NULL)
                continue;

            dynamic_list_add(dtu_channel_list, channel_info);
        }
    }
}

/**
 * @brief 翻转网络灯
 */
void flip_net_led(void* param)
{
    cm_gpio_set_level(NET_LED_GPIO, net_led_level);
    net_led_level = (net_led_level == CM_GPIO_LEVEL_HIGH) ? CM_GPIO_LEVEL_LOW : CM_GPIO_LEVEL_HIGH;
}

/**
 * @brief 串口接收回调，接收到消息后要做异步处理则需要copy，因为调用回调的地方会free掉
 * @param[in] msg 消息体
 */
static void handle_uart_receive(uart_ctx_t* ctx, uint8_t* data, uint32_t len)
{
    console_log("[main] receive uart %d data, content=%s", ctx->dev_num, data);

    if (dtu_channel_list == NULL || dtu_channel_list->size == 0)
    {
        console_log("[main] receive uart data, no channel bind");
        return;
    }

    // 获取需要转发的通道列表
    dynamic_list_t* send_channel_list = dynamic_list_init(sizeof(dtu_channel_info_t));
    for (uint32_t i = 0; i < dtu_channel_list->size; i++)
    {
        dtu_channel_info_t* channel_info = (dtu_channel_info_t*)dynamic_list_get(dtu_channel_list, i);
        if (channel_info->dev_num == ctx->dev_num)
        {
            dynamic_list_add(send_channel_list, channel_info);
        }
    }

    // 没有需要转发的通道
    if (send_channel_list->size == 0)
    {
        console_log("[main] receive uart %d data, no channel send", ctx->dev_num);
        dynamic_list_free(send_channel_list);
        return;
    }

    uint8_t* copy_data = (uint8_t*)copy_str_by_len((char*)data, len);
    if (copy_data == NULL)
    {
        console_log("[main] receive uart %d data, copy data error", ctx->dev_num);
        dynamic_list_free(send_channel_list);
        return;
    }

    // 创建gc数据
    gc_data_t* gc_data = gc_data_init(copy_data, send_channel_list->size);
    if (gc_data == NULL)
    {
        console_log("[main] receive uart %d data, malloc gc data failed", ctx->dev_num);
        dynamic_list_free(send_channel_list);
        return;
    }

    // 转发
    for (uint32_t i = 0; i < send_channel_list->size; i++)
    {
        dtu_channel_info_t* channel_info = (dtu_channel_info_t*)dynamic_list_get(send_channel_list, i);
        console_log("[main] receive uart data, dev_num=%d, transter to %s channel %d", ctx->dev_num, channel_info->type, channel_info->id);
        dtu_channel_send(channel_info, gc_data, len);
    }

    dynamic_list_free(send_channel_list);

    console_log("[main] receive uart %d data end", ctx->dev_num);
}

/**
 * @brief 处理通道接收数据
 *
 * @param[in] channel_info 通道信息
 * @param[in] data 接收到的数据
 * @param[in] len 接收到的数据长度
 */
static void handle_channel_receive(dtu_channel_info_t* channel_info, gc_data_t* data, size_t len)
{
    console_log("[main] receive %s channel %d data: %s, transfer to uart %d", channel_info->type, channel_info->id, (uint8_t*)data->data, channel_info->dev_num);
    uart_send_t* msg = (uart_send_t*)cm_malloc(sizeof(uart_send_t));
    msg->dev_num = channel_info->dev_num;
    msg->data = data;
    msg->len = len;
    osMessageQueuePut(uart_send_queue, &msg, 0, 0);
}

/**
 * @brief 处理uart发送消息
 */
static void handle_uart_send(void* param)
{
    while (TRUE)
    {
        uart_send_t* msg;
        if (uart_send_queue != NULL && osMessageQueueGet(uart_send_queue, &msg, 0, osWaitForever) == osOK)
        {
            gc_data_t* gc_data = msg->data;
            uint8_t* data = (uint8_t*)gc_data->data;
            console_log("[main] uart %d send: %s", msg->dev_num, data);
            cm_uart_write(msg->dev_num, data, msg->len, 1000);
            gc_try_free(gc_data);
            cm_free(msg);
        }
    }
}