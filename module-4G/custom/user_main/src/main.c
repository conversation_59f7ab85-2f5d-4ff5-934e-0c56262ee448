#include "main.h"
#include "dtu_config.h"
#include "dtu_channel.h"
#include "dtu_channel_uart.h"
#include "my_util.h"
#include "cm_os.h"
#include "cm_sys.h"
#include "cm_uart.h"
#include "cm_mem.h"
#include "cm_fs.h"
#include "cm_modem.h"
#include "cm_rtc.h"

// 函数向前声明
void main_task_func(void* param);
void flip_work_led(void* param);
static void handle_channel_receive(dtu_channel_info_t* channel_info, gc_data_t* data, size_t len);

// 主线程句柄
static osThreadId_t main_task_id = NULL;
// 通道列表
dynamic_list_t* dtu_channel_list = NULL;
// 工作灯状态
cm_gpio_level_e work_led_level = CM_GPIO_LEVEL_LOW;
// 工作灯闪烁定时器
static osTimerId_t work_led_timer = NULL;

/**
 * @brief 用户程序入口
 */
int cm_opencpu_entry(void* param)
{
    // 初始化网络灯
    cm_gpio_cfg_t gpio_cfg = {
        .direction = CM_GPIO_DIRECTION_OUTPUT,
        .pull = CM_GPIO_PULL_NONE,
    };
    cm_iomux_set_pin_func(NET_LED_PIN, CM_IOMUX_FUNC_FUNCTION1);
    cm_gpio_init(NET_LED_GPIO, &gpio_cfg);

    // 初始化WORK灯
    cm_gpio_cfg_t net_gpio_cfg = {
        .direction = CM_GPIO_DIRECTION_OUTPUT,
        .pull = CM_GPIO_PULL_NONE,
    };
    cm_iomux_set_pin_func(WORK_LED_PIN, CM_IOMUX_FUNC_FUNCTION1);
    cm_gpio_init(WORK_LED_GPIO, &net_gpio_cfg);
    work_led_timer = osTimerNew(flip_work_led, osTimerPeriodic, NULL, NULL);

    // 打印版本信息
    console_log("======= FENYDATA OPENCPU 1.0 =======");

    // SDK版本
    char buff[CM_VER_LEN] = { 0 };
    cm_sys_get_cm_ver(buff, CM_VER_LEN);
    console_log("SDK VERSION: %s", buff);

    // 文件系统占用情况
    cm_fs_system_info_t fs_status = { 0, 0 };
    cm_fs_getinfo(&fs_status);
    console_log("fs status: total:%d, free:%d", fs_status.total_size, fs_status.free_size);

    // 内存占用情况
    cm_heap_stats_t heap_status = { 0 };
    cm_mem_get_heap_stats(&heap_status);
    console_log("heap status: total:%d, free:%d", heap_status.total_size, heap_status.free);

    // 初始化gc线程
    gc_task_init();

    // 开启主线任务
    osThreadAttr_t app_task_attr = { 0 };
    app_task_attr.name = "main task";
    app_task_attr.stack_size = 1024 * 12;
    app_task_attr.priority = osPriorityNormal;
    main_task_id = osThreadNew((osThreadFunc_t)main_task_func, 0, &app_task_attr);

    // 主循环，如果没有这个，所有地方的timer都失效
    while (TRUE);

    return 0;
}

// 主线任务
void main_task_func(void* param)
{
    // 获取网络状态
    console_log("[main] waiting for network...");
    int try_times = 0;
    while (TRUE)
    {
        if (try_times > 10)
        {
            console_log("[main] network timeout");
            // TODO 致命：主任务直接退出，系统停止工作，让它一直检测就行
            return;
        }
        if (cm_modem_get_pdp_state(1) == 1)
        {
            console_log("[main] network ready");
            break;
        }
        osDelay(200);
        try_times++;
    }

    // 亮起网络灯
    cm_gpio_set_level(NET_LED_GPIO, CM_GPIO_LEVEL_HIGH);

    // 拉取配置
    console_log("[main] get dtu config start...");
    dtu_config_t dtu_config = { 0 };
    COMMON_RES_T res = get_dtu_config(&dtu_config);
    if (res != RES_SUCCESS)
    {
        console_log("[main] get cloud config failed");
        return;
    }
    console_log("[main] get dtu config success, version=%s", dtu_config.version);

    // 设置通道
    if (dtu_config.channel_list->size > 0)
    {
        // 关闭所有通道
        dtu_channel_close_all();

        // 清空绑定关系
        if (dtu_channel_list != NULL)
            dynamic_list_free(dtu_channel_list);

        dtu_channel_list = dynamic_list_init(sizeof(dtu_channel_info_t*));

        // 注册通道接收回调
        dtu_channel_register_callback(handle_channel_receive);

        for (int i = 0; i < dtu_config.channel_list->size; i++)
        {
            dtu_channel_t* channel = (dtu_channel_t*)dynamic_list_get(dtu_config.channel_list, i);
            if (!channel->enable)
                continue;

            // 注册通道
            dtu_channel_info_t* channel_info = dtu_channel_init(channel);
            if (channel_info == NULL)
                continue;

            dynamic_list_add(dtu_channel_list, channel_info);
        }

        // 启动工作灯闪烁
        osTimerStart(work_led_timer, get_ms(500));
    }
}

/**
 * @brief 翻转工作灯
 */
void flip_work_led(void* param)
{
    cm_gpio_set_level(WORK_LED_GPIO, work_led_level);
    work_led_level = (work_led_level == CM_GPIO_LEVEL_HIGH) ? CM_GPIO_LEVEL_LOW : CM_GPIO_LEVEL_HIGH;
}

/**
 * @brief 处理通道接收数据
 *
 * @param[in] channel_info 通道信息
 * @param[in] data 接收到的数据
 * @param[in] len 接收到的数据长度
 */
static void handle_channel_receive(dtu_channel_info_t* channel_info, gc_data_t* data, size_t len)
{
    console_log("[main] receive %s channel %d data start", channel_info->protocol, channel_info->id);

    console_log("[main] receive %s channel %d data: %s", channel_info->protocol, channel_info->id, bytes_to_hex(data->data, len));

    char* send_type = strcmp(channel_info->type, "collect") == 0 ? "transfer" : "collect";

    // 获取需要转发的通道列表
    dynamic_list_t* send_channel_list = dynamic_list_init(sizeof(dtu_channel_info_t));
    for (uint32_t i = 0; i < dtu_channel_list->size; i++)
    {
        dtu_channel_info_t* channel_info = (dtu_channel_info_t*)dynamic_list_get(dtu_channel_list, i);
        if (strcmp(channel_info->type, send_type) == 0)
        {
            dynamic_list_add(send_channel_list, channel_info);
        }
    }

    // 没有需要转发的通道
    if (send_channel_list->size == 0)
    {
        console_log("[main] receive %s channel %d data, no channel send", channel_info->protocol, channel_info->id);
        dynamic_list_free(send_channel_list);
        gc_try_free(data);
        return;
    }

    // 复制数据
    uint8_t* copy_data = (uint8_t*)copy_str_by_len((char*)data->data, len);
    if (copy_data == NULL)
    {
        console_log("[main] receive %s channel %d data, copy data failed", channel_info->protocol, channel_info->id);
        dynamic_list_free(send_channel_list);
        gc_try_free(data);
        return;
    }

    // 回收数据
    gc_try_free(data);

    // 创建gc数据
    gc_data_t* gc_data = gc_data_init(copy_data, send_channel_list->size);
    if (gc_data == NULL)
    {
        console_log("[main] receive %s channel %d data, init gc data failed", channel_info->protocol, channel_info->id);
        dynamic_list_free(send_channel_list);
        cm_free(copy_data);
        return;
    }

    // 转发
    for (uint32_t i = 0; i < send_channel_list->size; i++)
    {
        dtu_channel_info_t* send_channel = (dtu_channel_info_t*)dynamic_list_get(send_channel_list, i);
        console_log("[main] receive %s channel %d data, transfer to %s channel %d", channel_info->protocol, channel_info->id, send_channel->protocol, send_channel->id);
        dtu_channel_send(send_channel, gc_data, len);
    }

    // 释放转发通道列表
    dynamic_list_free(send_channel_list);

    console_log("[main] receive %s channel %d data end", channel_info->protocol, channel_info->id);
}