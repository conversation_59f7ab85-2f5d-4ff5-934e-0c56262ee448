#include "common.h"
#include "my_util.h"
#include "dtu_channel.h"
#include "dtu_channel_uart.h"
#include "dtu_channel_tcp.h"
#include "dtu_channel_udp.h"
#include "dtu_channel_mqtt.h"
#include "dtu_channel_http.h"
#include "pikaScript.h"
#include "PikaVM.h"

/**
 * @brief 模板处理参数
 */
typedef struct
{
    dtu_channel_info_t* channel_info;
    gc_data_t* data;
    size_t len;
} dtu_channel_template_msg_t;

// 函数向前声明
void handle_channel_receive(void* ctx, uint8_t* data, size_t len);
void handle_channel_receive_teamplate(void* params);
COMMON_RES_T dtu_channel_do_send(dtu_channel_info_t* channel_info, gc_data_t* data, size_t len);
void handle_channel_send_teamplate(void* params);

// 通道信息列表
dynamic_list_t* channel_info_list = NULL;
// 回调函数列表
dynamic_list_t* callback_list = NULL;
// 发送模板处理线程
osThreadId_t send_template_thread_id = NULL;
// 发送模板处理队列
osMessageQueueId_t send_template_queue_id = NULL;
// 接收模板处理线程
osThreadId_t receive_template_thread_id = NULL;
// 接收模板处理队列
osMessageQueueId_t receive_template_queue_id = NULL;

/**
 * @brief 注册通道回调函数
 *
 * @param callback 通道接收回调函数
 */
void dtu_channel_register_callback(dtu_receive_callback_t callback)
{
    if (callback == NULL)
        return;

    if (callback_list == NULL)
        callback_list = dynamic_list_init(sizeof(dtu_receive_callback_t));

    dynamic_list_add(callback_list, callback);
}

/**
 * @brief 注册通道
 *
 * @param channel 通道配置
 * @param callback 通道接收回调函数
 *
 * @return channel_info_t* 通道信息
 */
dtu_channel_info_t* dtu_channel_init(dtu_channel_t* channel_param)
{
    uint8_t channel_id = next_global_id();
    char* protocol = channel_param->protocol;

    console_log("[dtu] register %s channel %d start", protocol, channel_id);

    // 获取实际的上下文对象
    void* channel_ctx = NULL;
    if (strcmp("rs485/rs232", protocol) == 0)
    {
        channel_ctx = uart_channel_init((dtu_uart_param_t*)channel_param->param, channel_param->collect_channel_num, handle_channel_receive);
        // 串口不能占用通道id
        if (channel_ctx != NULL)
        {
            reset_global_id(channel_id - 1);
        }
    }
    else if (strcmp("tcp", protocol) == 0)
    {
        channel_ctx = tcp_channel_init((dtu_socket_param_t*)channel_param->param, channel_id, handle_channel_receive);
    }
    else if (strcmp("udp", protocol) == 0)
    {
        channel_ctx = udp_channel_init((dtu_socket_param_t*)channel_param->param, channel_id, handle_channel_receive);
    }
    else if (strcmp("mqtt", protocol) == 0)
    {
        channel_ctx = mqtt_channel_init((dtu_mqtt_param_t*)channel_param->param, channel_id, handle_channel_receive);
    }
    else if (strcmp("http", protocol) == 0)
    {
        channel_ctx = http_channel_init((dtu_http_param_t*)channel_param->param, channel_id, handle_channel_receive);
    }

    if (channel_ctx == NULL)
    {
        reset_global_id(channel_id - 1);
        console_log("[dtu] register %s channel %d failed, no ctx or invalid type", protocol, channel_id);
        return NULL;
    }

    dtu_channel_info_t* info = (dtu_channel_info_t*)cm_malloc(sizeof(dtu_channel_info_t));
    if (info == NULL)
    {
        reset_global_id(channel_id - 1);
        console_log("[dtu] register %s channel %d failed, malloc channel info failed", protocol, channel_id);
        return NULL;
    }
    info->id = channel_id;
    info->protocol = protocol;
    info->type = channel_param->type;
    info->ctx = channel_ctx;
    info->collect_channel_num = channel_param->collect_channel_num;
    info->param = channel_param;

    // 创建接收模板处理线程
    if (not_empty(channel_param->receive_template) && receive_template_thread_id == NULL)
    {
        receive_template_queue_id = osMessageQueueNew(10, sizeof(dtu_channel_template_msg_t*), NULL);
        osThreadAttr_t receive_template_thread_attr = { 0 };
        receive_template_thread_attr.name = "dtu_channel_receive_template_thread";
        receive_template_thread_attr.stack_size = 1024 * 20;
        receive_template_thread_attr.priority = osPriorityNormal;
        receive_template_thread_id = osThreadNew(handle_channel_receive_teamplate, NULL, &receive_template_thread_attr);
    }

    // 创建发送模板处理线程
    if (not_empty(channel_param->send_template) && send_template_thread_id == NULL)
    {
        send_template_queue_id = osMessageQueueNew(10, sizeof(dtu_channel_template_msg_t*), NULL);
        osThreadAttr_t send_template_thread_attr = { 0 };
        send_template_thread_attr.name = "dtu_channel_send_template_thread";
        send_template_thread_attr.stack_size = 1024 * 20;
        send_template_thread_attr.priority = osPriorityNormal;
        send_template_thread_id = osThreadNew(handle_channel_send_teamplate, NULL, &send_template_thread_attr);
    }

    // 保存通道信息
    if (channel_info_list == NULL)
        channel_info_list = dynamic_list_init(sizeof(dtu_channel_info_t));
    dynamic_list_add(channel_info_list, info);

    console_log("[dtu] register %s channel %d success", protocol, channel_id);

    return info;
}

/**
 * @brief 发送数据
 *
 * @param channel_info 通道句柄
 * @param data 数据
 * @param len 数据长度
 */
COMMON_RES_T dtu_channel_send(dtu_channel_info_t* channel_info, gc_data_t* data, size_t len)
{
    // 如果有发送模板，则用发送模板处理后再发送
    if (not_empty(channel_info->param->send_template))
    {
        // 申请内存
        dtu_channel_template_msg_t* msg = (dtu_channel_template_msg_t*)cm_malloc(sizeof(dtu_channel_template_msg_t));
        if (msg == NULL)
        {
            console_log("[dtu] %s channel %d send, template translate failed, malloc template msg failed", channel_info->protocol, channel_info->id);
            return RES_ERROR;
        }
        msg->channel_info = channel_info;
        msg->data = data;
        msg->len = len;
        osMessageQueuePut(send_template_queue_id, &msg, 0, 0);
        return RES_SUCCESS;
    }
    return dtu_channel_do_send(channel_info, data, len);
}

/**
 * @brief 执行发送数据
 *
 * @param channel_info 通道句柄
 * @param data 数据
 * @param len 数据长度
 */
COMMON_RES_T dtu_channel_do_send(dtu_channel_info_t* channel_info, gc_data_t* data, size_t len)
{
    console_log("[dtu] %s channel %d do send start", channel_info->protocol, channel_info->id);

    COMMON_RES_T res = RES_ERROR;
    if (strcmp("rs485/rs232", channel_info->protocol) == 0)
    {
        res = uart_channel_send((uart_ctx_t*)channel_info->ctx, data, len);
    }
    else if (strcmp("tcp", channel_info->protocol) == 0)
    {
        res = tcp_channel_send((tcp_channel_ctx_t*)channel_info->ctx, data, len);
    }
    else if (strcmp("udp", channel_info->protocol) == 0)
    {
        res = udp_channel_send((udp_channel_ctx_t*)channel_info->ctx, data, len);
    }
    else if (strcmp("mqtt", channel_info->protocol) == 0)
    {
        res = mqtt_channel_send((mqtt_channel_ctx_t*)channel_info->ctx, data, len);
    }
    else if (strcmp("http", channel_info->protocol) == 0)
    {
        res = http_channel_send((http_channel_ctx_t*)channel_info->ctx, data, len);
    }

    console_log("[dtu] %s channel %d do send end", channel_info->protocol, channel_info->id);

    return res;
}

/**
 * @brief 关闭所有通道
 */
void dtu_channel_close_all(void)
{
    if (channel_info_list == NULL)
        return;

    for (uint32_t i = 0; i < channel_info_list->size; i++)
    {
        dtu_channel_info_t* channel_info = (dtu_channel_info_t*)dynamic_list_get(channel_info_list, i);

        if (strcmp("rs485/rs232", channel_info->protocol) == 0)
        {
            uart_channel_close((uart_ctx_t*)channel_info->ctx);
        }
        else if (strcmp("tcp", channel_info->protocol) == 0)
        {
            tcp_channel_close((tcp_channel_ctx_t*)channel_info->ctx);
        }
        else if (strcmp("udp", channel_info->protocol) == 0)
        {
            udp_channel_close((udp_channel_ctx_t*)channel_info->ctx);
        }
        else if (strcmp("mqtt", channel_info->protocol) == 0)
        {
            mqtt_channel_close((mqtt_channel_ctx_t*)channel_info->ctx);
        }
        else if (strcmp("http", channel_info->protocol) == 0)
        {
            http_channel_close((http_channel_ctx_t*)channel_info->ctx);
        }

        free_ptr(channel_info);
    }
    dynamic_list_free(channel_info_list);
    channel_info_list = NULL;
}

/**
 * @brief 处理通道接收数据
 *
 * @param id 通道上下文
 * @param data 数据
 * @param len 数据长度
 */
void handle_channel_receive(void* ctx, uint8_t* data, size_t len)
{
    console_log("[dtu] channel receive start");

    if (channel_info_list == NULL)
    {
        console_log("[dtu] channel receive end, no channel info list");
        return;
    }
    if (callback_list == NULL || callback_list->size == 0)
    {
        console_log("[dtu] channel receive end, no callback list");
        return;
    }

    // 查找对应的通道绑定信息
    dtu_channel_info_t* channel_info = NULL;
    for (uint32_t i = 0; i < channel_info_list->size; i++)
    {
        dtu_channel_info_t* item = (dtu_channel_info_t*)dynamic_list_get(channel_info_list, i);
        if (item->ctx == ctx)
        {
            channel_info = item;
            break;
        }
    }

    if (channel_info == NULL)
    {
        console_log("[dtu] channel receive end, channel info not found");
        return;
    }
    console_log("[dtu] channel receive, found %s channel %d", channel_info->protocol, channel_info->id);

    gc_data_t* gc_data = gc_data_init(data, callback_list->size);
    if (gc_data == NULL)
    {
        console_log("[dtu] channel receive end, gc data init failed");
        return;
    }

    // 如果有接收模板，则用接收模板处理后再调用回调函数
    dtu_channel_t* channel_param = channel_info->param;
    if (not_empty(channel_param->receive_template))
    {
        dtu_channel_template_msg_t* msg = (dtu_channel_template_msg_t*)cm_malloc(sizeof(dtu_channel_template_msg_t));
        if (msg == NULL)
        {
            console_log("[dtu] %s channel %d receive, template translate failed, malloc template msg failed", channel_info->protocol, channel_info->id);
            return;
        }
        msg->channel_info = channel_info;
        msg->data = gc_data;
        msg->len = len;
        osMessageQueuePut(receive_template_queue_id, &msg, 0, 0);
        return;
    }

    // 调用回调函数
    for (uint32_t i = 0; i < callback_list->size; i++)
    {
        dtu_receive_callback_t callback = (dtu_receive_callback_t)dynamic_list_get(callback_list, i);
        callback(channel_info, gc_data, len);
    }

    console_log("[dtu] %s channel %d receive end", channel_info->protocol, channel_info->id);
}

/**
 * @brief 处理通道接收模板数据
 */
void handle_channel_receive_teamplate(void* params)
{
    console_log("[dtu] channel receive template thread start");
    while (TRUE)
    {
        dtu_channel_template_msg_t* msg = NULL;
        if (osMessageQueueGet(receive_template_queue_id, &msg, 0, osWaitForever) == osOK)
        {
            dtu_channel_info_t* channel_info = msg->channel_info;
            dtu_channel_t* channel_param = channel_info->param;
            gc_data_t* gc_data = msg->data;
            size_t len = msg->len;

            // 释放msg对象
            free_ptr(msg);

            console_log("[dtu] %s channel %d receive template start", channel_info->protocol, channel_info->id);

            // 初始化pikaPython
            PikaObj* pikaMain = newRootObj("pikaMain", New_PikaMain);
            extern unsigned char pikaModules_py_a[];
            obj_linkLibrary(pikaMain, pikaModules_py_a);

            // 设置参数
            obj_setBytes(pikaMain, "data", (uint8_t*)gc_data->data, len);

            // 运行模板
            pikaVM_run(pikaMain, channel_param->receive_template);

            // 获取结果
            size_t res_data_len = obj_getBytesSize(pikaMain, "data");
            if (res_data_len <= 0)
            {
                console_log("[dtu] %s channel %d receive template error, template res is empty", channel_info->protocol, channel_info->id);
                obj_deinit(pikaMain);
                continue;
            }
            uint8_t* res_data = (uint8_t*)cm_malloc(res_data_len + 1);
            if (res_data == NULL)
            {
                console_log("[dtu] %s channel %d receive template error, malloc template res error", channel_info->protocol, channel_info->id);
                obj_deinit(pikaMain);
                continue;
            }
            obj_loadBytes(pikaMain, "data", res_data);
            res_data[res_data_len] = '\0';

            // 销毁实例
            obj_deinit(pikaMain);

            gc_data->data = res_data;

            // 调用回调函数
            for (uint32_t i = 0; i < callback_list->size; i++)
            {
                dtu_receive_callback_t callback = (dtu_receive_callback_t)dynamic_list_get(callback_list, i);
                callback(channel_info, gc_data, res_data_len);
            }

            console_log("[dtu] %s channel %d receive template end", channel_info->protocol, channel_info->id);
        }
    }
}

/**
 * @brief 处理通道发送模板数据
 */
void handle_channel_send_teamplate(void* params)
{
    console_log("[dtu] channel send template thread start");
    while (TRUE)
    {
        dtu_channel_template_msg_t* msg = NULL;
        if (osMessageQueueGet(send_template_queue_id, &msg, 0, osWaitForever) == osOK)
        {
            dtu_channel_info_t* channel_info = msg->channel_info;
            dtu_channel_t* channel_param = channel_info->param;
            gc_data_t* gc_data = msg->data;
            size_t len = msg->len;

            // 释放msg对象
            free_ptr(msg);

            console_log("[dtu] %s channel %d send template start", channel_info->protocol, channel_info->id);

            // 初始化pikaPython
            PikaObj* pikaMain = newRootObj("pikaMain", New_PikaMain);
            extern unsigned char pikaModules_py_a[];
            obj_linkLibrary(pikaMain, pikaModules_py_a);

            // 设置参数
            obj_setBytes(pikaMain, "data", (uint8_t*)gc_data->data, len);

            // 释放内存
            gc_try_free(gc_data);

            // 运行模板
            pikaVM_run(pikaMain, channel_param->send_template);

            // 获取结果
            size_t res_data_len = obj_getBytesSize(pikaMain, "data");
            if (res_data_len <= 0)
            {
                console_log("[dtu] %s channel %d send template error, template res is empty", channel_info->protocol, channel_info->id);
                obj_deinit(pikaMain);
                continue;
            }
            uint8_t* res_data = (uint8_t*)cm_malloc(res_data_len + 1);
            if (res_data == NULL)
            {
                console_log("[dtu] %s channel %d send template error, malloc template res error", channel_info->protocol, channel_info->id);
                obj_deinit(pikaMain);
                continue;
            }
            obj_loadBytes(pikaMain, "data", res_data);
            res_data[res_data_len] = '\0';

            // 销毁实例
            obj_deinit(pikaMain);

            // 创建gc_data
            gc_data_t* res_gc_data = gc_data_init(res_data, 1);
            if (res_gc_data == NULL)
            {
                console_log("[dtu] %s channel %d send template error, gc_data_init error", channel_info->protocol, channel_info->id);
                free_ptr(res_data);
                continue;
            }

            // 调用回调函数
            dtu_channel_do_send(channel_info, res_gc_data, res_data_len);

            console_log("[dtu] %s channel %d send template end", channel_info->protocol, channel_info->id);
        }
    }
}