#include "dtu_channel.h"
#include "dtu_channel_uart.h"
#include "common.h"
#include "my_util.h"
#include "cm_uart.h"
#include "cm_sys.h"
#include "stdio.h"
#include "stdlib.h"
#include "stdarg.h"
#include "cm_os.h"
#include "cm_mem.h"
#include "cm_virt_at.h"
#include "cm_iomux.h"

// 函数向前声明
uint8_t get_dev_num_by_collect_channel_num(uint8_t collect_channel_num);
static void uart_event_callback(void* param, uint32_t type);
static void uart_read_thread(void* params);
static void uart_pack_timer(void* params);
static void uart_send_msg_to_consume_task(uart_ctx_t* ctx, uint8_t* data, uint32_t len);
static void uart_consume_thread(void* params);
static void uart_auto_pull_thread(void* params);
static void uart_send_thread(void* params);
static void set_virt_at_log2cat(uint32_t uart_num);
void set_uart_iomux_pin_func(uint8_t dev_num);

// 串口上下文列表
dynamic_list_t* uart_ctx_list = NULL;

/**
 * @brief 串口初始化
 * @param config 配置信息
 * @param collect_channel_num 采集通道编号
 * @param callback 接收回调
 * @return 串口上下文，NULL则为初始化失败
 */
uart_ctx_t* uart_channel_init(dtu_uart_param_t* config, uint8_t collect_channel_num, data_receive_callback_t callback)
{
    uint8_t dev_num = get_dev_num_by_collect_channel_num(collect_channel_num);
    console_log("[uart %d] init", dev_num);

    if (dev_num >= CM_UART_DEV_NUM)
    {
        console_log("[uart %d] init dev_num not valid", dev_num);
        return NULL;
    }

    // 如果之前已经初始化，则先释放
    if (uart_ctx_list != NULL)
    {
        for (uint32_t i = 0; i < uart_ctx_list->size; i++)
        {
            uart_ctx_t* item = (uart_ctx_t*)dynamic_list_get(uart_ctx_list, i);
            if (item->dev_num == dev_num)
            {
                uart_channel_close(item);
            }
        }
    }

    // 申请内存
    uart_ctx_t* ctx = (uart_ctx_t*)cm_malloc(sizeof(uart_ctx_t));
    if (ctx == NULL)
    {
        console_log("[uart %d] init end, malloc ctx fail", dev_num);
        return NULL;
    }

    ctx->config = config;
    ctx->dev_num = dev_num;
    ctx->callback = callback;

    // 读取线程信号量
    ctx->read_sem = osSemaphoreNew(1, 0, NULL);

    // 创建读取线程
    osThreadAttr_t receive_thread_attr = { 0 };
    char receive_thread_name[30] = { 0 };
    sprintf(receive_thread_name, "uart %d receive thread", dev_num);
    receive_thread_attr.name = receive_thread_name;
    receive_thread_attr.stack_size = 2048;
    receive_thread_attr.priority = osPriorityNormal;
    ctx->read_thread_id = osThreadNew(uart_read_thread, ctx, &receive_thread_attr);

    // 分包定时器
    ctx->receive_pack_timer_id = osTimerNew(uart_pack_timer, osTimerOnce, ctx, NULL);

    // 自动拉取线程
    if (config->collect_interval > 0 && config->cmd_list->size > 0)
    {
        ctx->auto_pull_flag = osEventFlagsNew(NULL);
        osThreadAttr_t auto_pull_thread_attr = { 0 };
        char auto_pull_thread_name[30] = { 0 };
        sprintf(auto_pull_thread_name, "uart %d auto pull thread", dev_num);
        auto_pull_thread_attr.name = auto_pull_thread_name;
        auto_pull_thread_attr.stack_size = 2048;
        auto_pull_thread_attr.priority = osPriorityNormal;
        ctx->read_thread_id = osThreadNew(uart_auto_pull_thread, ctx, &auto_pull_thread_attr);
    }

    // 创建发送队列
    ctx->send_queue_id = osMessageQueueNew(10, sizeof(comm_gc_msg_t*), NULL);

    // 创建发送线程
    osThreadAttr_t send_thread_attr = { 0 };
    char send_thread_name[30];
    sprintf(send_thread_name, "uart %d send thread", dev_num);
    send_thread_attr.name = send_thread_name;
    send_thread_attr.stack_size = 1024;
    send_thread_attr.priority = osPriorityNormal;
    ctx->read_thread_id = osThreadNew((osThreadFunc_t)uart_send_thread, ctx, &send_thread_attr);

    // 创建消费队列
    ctx->consume_queue_id = osMessageQueueNew(10, sizeof(comm_msg_t*), NULL);

    // 创建消费线程
    osThreadAttr_t consume_thread_attr = { 0 };
    char consume_thread_name[30];
    sprintf(consume_thread_name, "uart %d consume thread", dev_num);
    consume_thread_attr.name = consume_thread_name;
    consume_thread_attr.stack_size = 2048;
    consume_thread_attr.priority = osPriorityNormal;
    ctx->consume_thread_id = osThreadNew((osThreadFunc_t)uart_consume_thread, ctx, &consume_thread_attr);

    /* 若为UART2，需要先将log打印从debug切换到usb */
    set_virt_at_log2cat(dev_num);

    /* 配置引脚复用 */
    set_uart_iomux_pin_func(dev_num);

    /* 配置串口唤醒，只有UART0具有串口唤醒功能 */
    if (dev_num == CM_UART_DEV_0)
    {
        /* 配置uart唤醒功能，使能边沿检测才具备唤醒功能，仅主串口具有唤醒功能，用于唤醒的数据并不能被uart接收，请在唤醒后再进行uart数传 */
        cm_iomux_set_pin_cmd(UART_WEKEUP_PIN, CM_IOMUX_PINCMD1_LPMEDEG, CM_IOMUX_PINCMD1_FUNC1_LPM_EDGE_RISE);
    }

    // 上下文加入列表
    if (uart_ctx_list == NULL)
        uart_ctx_list = dynamic_list_init(sizeof(uart_ctx_t));
    dynamic_list_add(uart_ctx_list, ctx);

    /* 注册事件和回调函数 */
    cm_uart_event_t event = { CM_UART_EVENT_TYPE_RX_ARRIVED, ctx, uart_event_callback };
    int32_t res = cm_uart_register_event(dev_num, &event);
    if (res != RES_SUCCESS)
    {
        console_log("[uart %d] register event err, res = %d", dev_num, res);
        uart_channel_close(ctx);
        return NULL;
    }

    /* 开启串口 */
    cm_uart_cfg_t uart_config = {
        .baudrate = config->baud_rate,
        .byte_size = config->byte_size,
        .stop_bit = config->stop_bit,
        .parity = config->parity,
        .flow_ctrl = CM_UART_FLOW_CTRL_NONE,
        .is_lpuart = 0,
        .rxrb_buf_size = 0,
        .fc_high_threshold = 0,
        .fc_low_threshold = 0
    };
    res = cm_uart_open(dev_num, &uart_config);
    if (res != RES_SUCCESS)
    {
        console_log("[uart %d] open err, res = %d", dev_num, res);
        uart_channel_close(ctx);
        return NULL;
    }

    console_log("[uart %d] init success", dev_num);

    return ctx;
}

/**
 * @brief 串口通道发送
 *
 * @param ctx uart通道上下文
 * @param data 数据
 * @param len 数据长度
 */
COMMON_RES_T uart_channel_send(uart_ctx_t* ctx, gc_data_t* data, size_t len)
{
    console_log("[uart %d] send start", ctx->dev_num);

    comm_gc_msg_t* msg = (comm_gc_msg_t*)cm_malloc(sizeof(comm_gc_msg_t));
    if (msg == NULL)
    {
        console_log("[uart %d] send failed, malloc msg failed", ctx->dev_num);
        return RES_ERROR;
    }
    msg->data = data;
    msg->len = len;

    osMessageQueuePut(ctx->send_queue_id, &msg, 0, 0);

    console_log("[uart %d] send end", ctx->dev_num);

    return RES_SUCCESS;
}

/**
 * @brief 串口通道关闭
 *
 * @param ctx 串口通道上下文
 */
void uart_channel_close(uart_ctx_t* ctx)
{
    cm_uart_close(ctx->dev_num);

    if (ctx->send_thread_id != NULL)
    {
        osThreadTerminate(ctx->send_thread_id);
        ctx->send_thread_id = NULL;
    }

    if (ctx->send_queue_id != NULL)
    {
        osMessageQueueDelete(ctx->send_queue_id);
        ctx->send_queue_id = NULL;
    }

    if (ctx->consume_thread_id != NULL)
    {
        osThreadTerminate(ctx->consume_thread_id);
        ctx->consume_thread_id = NULL;
    }

    if (ctx->consume_queue_id != NULL)
    {
        osMessageQueueDelete(ctx->consume_queue_id);
        ctx->consume_queue_id = NULL;
    }

    if (ctx->receive_pack_timer_id != NULL)
    {
        osTimerStop(ctx->receive_pack_timer_id);
        osTimerDelete(ctx->receive_pack_timer_id);
        ctx->receive_pack_timer_id = NULL;
    }

    if (ctx->read_thread_id != NULL)
    {
        osThreadTerminate(ctx->read_thread_id);
        ctx->read_thread_id = NULL;
    }

    if (ctx->read_sem != NULL)
    {
        osSemaphoreDelete(ctx->read_sem);
        ctx->read_sem = NULL;
    }

    if (ctx->receive_buf_len > 0)
    {
        free_ptr(ctx->receive_buf);
        ctx->receive_buf = NULL;
        ctx->receive_buf_len = 0;
    }

    ctx->config = NULL;
    ctx->callback = NULL;

    dynamic_list_remove(uart_ctx_list, ctx);

    free_ptr(ctx);
}

/**
 * @brief 数据串口事件回调
 * 回调函数中不可输出LOG、串口打印、执行复杂任务或消耗过多资源
 * 建议以信号量或消息队列形式控制其他线程执行任务
 *
 * @param[in] param 参数(试过，没用)
 * @param[in] type 类型
 */
static void uart_event_callback(void* param, uint32_t type)
{
    uart_ctx_t* ctx = (uart_ctx_t*)param;
    console_log("uart_event_callback dev_num:%d type:%d", ctx->dev_num, type);
    if (CM_UART_EVENT_TYPE_RX_ARRIVED & type)
    {
        /* 收到接收事件，触发其他线程执行读取数据 */
        osSemaphoreRelease(ctx->read_sem);
    }
}

/**
 * @brief 串口接收任务处理
 * @param[in] params 参数
 */
static void uart_read_thread(void* param)
{
    uart_ctx_t* ctx = (uart_ctx_t*)param;
    dtu_uart_param_t config = *(ctx->config);
    uint8_t dev_num = ctx->dev_num;
    console_log("uart_read_thread dev_num:%d start", dev_num);
    while (TRUE)
    {
        // 等待触发事件
        osSemaphoreAcquire(ctx->read_sem, osWaitForever);

        console_log("[uart %d] receive start", dev_num);

        // 读取串口缓存区数据长度
        uint32_t data_len = cm_uart_get_rxrb_data_len(dev_num);

        // 获取对应串口的缓存信息
        char* current_buffer = ctx->receive_buf;
        uint32_t current_len = ctx->receive_buf_len;

        // 尝试重新分配内存
        char* new_buffer = (char*)cm_realloc(current_buffer, current_len + data_len + 1);
        if (new_buffer == NULL)
        {
            console_log("[uart %d] receive realloc failed", dev_num);
            free_ptr(current_buffer);
            ctx->receive_buf = NULL;
            ctx->receive_buf_len = 0;
            continue;
        }
        current_buffer = new_buffer;

        // 读取串口缓存区数据，并清空串口缓存区
        data_len = cm_uart_read(dev_num, current_buffer + current_len, data_len, 1000);
        cm_uart_clean(dev_num);

        //更新变量信息
        current_len += data_len;
        current_buffer[current_len] = '\0';

        // 如果配置了数据包最大长度，则按照最大长度进行分包
        if (config.pack_max_len > 1)
        {
            while (current_len > config.pack_max_len)
            {
                // 复制分包数据
                int send_buffer_len = config.pack_max_len;
                char* send_buffer = copy_str_by_len(current_buffer, send_buffer_len);
                if (send_buffer == NULL)
                {
                    console_log("[uart %d] receive pack by max len malloc send buffer failed", dev_num);
                    free_ptr(current_buffer);
                    ctx->receive_buf = NULL;
                    ctx->receive_buf_len = 0;
                    break;
                }

                // 发送分包
                console_log("[uart %d] send pack, len=%d", dev_num, send_buffer_len);
                uart_send_msg_to_consume_task(ctx, (uint8_t*)send_buffer, send_buffer_len);

                // 剩余包
                char* rest_buffer = NULL;
                int rest_buffer_len = current_len - send_buffer_len;
                if (rest_buffer_len > 0)
                {
                    rest_buffer = copy_str_by_len(current_buffer + send_buffer_len, rest_buffer_len);
                    if (rest_buffer == NULL)
                    {
                        console_log("[uart %d] receive pack by max len malloc rest buffer failed", dev_num);
                        free_ptr(current_buffer);
                        ctx->receive_buf = NULL;
                        ctx->receive_buf_len = 0;
                        break;
                    }
                }

                free_ptr(current_buffer);

                current_buffer = rest_buffer;
                current_len = rest_buffer_len;
            }
        }

        // 启动定时器
        osTimerStart(ctx->receive_pack_timer_id, get_ms(config.pack_timeout));

        // 保存当前数据
        ctx->receive_buf = current_buffer;
        ctx->receive_buf_len = current_len;

        console_log("[uart %d] receive end, current_len=%d", dev_num, current_len);
    }
}

/**
 * @brief 串口分包超时处理
 */
static void uart_pack_timer(void* params)
{
    uart_ctx_t* ctx = (uart_ctx_t*)params;
    if (ctx->receive_buf_len > 0)
    {
        uart_send_msg_to_consume_task(ctx, (uint8_t*)ctx->receive_buf, ctx->receive_buf_len);
        ctx->receive_buf = NULL;
        ctx->receive_buf_len = 0;

        // 触发ACK
        if (ctx->auto_pull_flag != NULL)
        {
            osEventFlagsSet(ctx->auto_pull_flag, AUTO_PULL_FLAG_ACK_SUCCESS);
        }
    }
}

/**
 * @brief 串口数据接收完成，发送给消费任务
 *
 * @param ctx 串口上下文
 * @param data 数据
 * @param len 数据大小
 */
static void uart_send_msg_to_consume_task(uart_ctx_t* ctx, uint8_t* data, uint32_t len)
{
    console_log("[uart %d] send msg to consume task, len=%d", ctx->dev_num, len);

    comm_msg_t* msg = (comm_msg_t*)cm_malloc(sizeof(comm_msg_t));
    if (msg == NULL)
    {
        console_log("[uart %d] send msg to consume task, malloc msg failed", ctx->dev_num);
        return;
    }

    msg->data = data;
    msg->len = len;

    // 消息放入队列
    osMessageQueuePut(ctx->consume_queue_id, &msg, 0, 0);

    console_log("[uart %d] send msg to consume task end", ctx->dev_num);
}

/**
 * @brief 串口消费处理任务
 */
static void uart_consume_thread(void* params)
{
    uart_ctx_t* ctx = (uart_ctx_t*)params;

    console_log("[uart %d] consume task start", ctx->dev_num);
    while (TRUE)
    {
        comm_msg_t* msg = NULL;
        if (osMessageQueueGet(ctx->consume_queue_id, &msg, NULL, osWaitForever) == osOK)
        {
            console_log("[uart %d] consume task receive msg start", ctx->dev_num);
            ctx->callback(ctx, msg->data, msg->len);
            console_log("[uart %d] consume task receive msg end", ctx->dev_num);
            free_ptr(msg);
        }
    }
}

/**
 * @brief 串口定时拉取任务处理
 */
static void uart_auto_pull_thread(void* params)
{
    uart_ctx_t* ctx = (uart_ctx_t*)params;

    console_log("[uart %d] auto pull thread start", ctx->dev_num);

    dtu_uart_param_t* config = ctx->config;
    while (TRUE)
    {
        console_log("[uart %d] auto pull start", ctx->dev_num);

        for (uint8_t i = 0; i < config->cmd_list->size; i++)
        {
            dtu_cmd_t* cmd = (dtu_cmd_t*)dynamic_list_get(config->cmd_list, i);
            char* cmd_str = cmd->cmd;

            if (cmd_str == NULL || cmd_str[0] == '\0')
                continue;

            console_log("[uart %d] auto pull, cmd=%s", ctx->dev_num, cmd_str);

            size_t send_data_len = strlen(cmd_str);
            uint8_t* send_data = (uint8_t*)cm_malloc(send_data_len);
            if (send_data == NULL)
            {
                console_log("[uart %d] auto pull, malloc send data failed", ctx->dev_num);
                continue;
            }
            memset(send_data, 0, send_data_len);
            size_t len = hex_to_bytes((uint8_t*)cmd_str, send_data, send_data_len, cmd->crc);
            if (len > 0)
            {
                gc_data_t* gc_data = gc_data_init(send_data, 1);
                if (gc_data == NULL)
                {
                    free_ptr(send_data);
                    console_log("[uart %d] auto pull, gc data init failed", ctx->dev_num);
                    continue;
                }

                // 设置发送标志为等待
                osEventFlagsSet(ctx->auto_pull_flag, AUTO_PULL_FLAG_SEND_WAIT);

                // 发送数据
                uart_channel_send(ctx, gc_data, len);

                // 等待发送完毕
                osEventFlagsWait(ctx->auto_pull_flag, AUTO_PULL_FLAG_SEND_SUCCESS, osFlagsWaitAny, osWaitForever);

                // 设置响应标志为等待
                osEventFlagsSet(ctx->auto_pull_flag, AUTO_PULL_FLAG_ACK_WAIT);
                
                // 等待响应
                osEventFlagsWait(ctx->auto_pull_flag, AUTO_PULL_FLAG_ACK_SUCCESS, osFlagsWaitAny, get_ms(config->collect_ack_timeout));
            }
        }

        console_log("[uart %d] auto pull end", ctx->dev_num);

        osDelay(get_ms(config->collect_interval));
    }
}

/**
 * @brief 串口发送线程
 */
static void uart_send_thread(void* params)
{
    uart_ctx_t* ctx = (uart_ctx_t*)params;

    console_log("[uart %d] send thread start", ctx->dev_num);
    while (TRUE)
    {
        comm_gc_msg_t* msg = NULL;
        if (osMessageQueueGet(ctx->send_queue_id, &msg, 0, osWaitForever) == osOK)
        {
            console_log("[uart %d] send thread, send start", ctx->dev_num);

            size_t len = msg->len;
            gc_data_t* gc_data = msg->data;
            uint8_t* data = (uint8_t*)gc_data->data;

            free_ptr(msg);

            // 分批次发送
            int part_size = 64;
            char buff[part_size];
            int send_len = 0;
            int buff_len;
            while (send_len < len)
            {
                memset(buff, 0, part_size);
                buff_len = send_len + part_size > len ? len - send_len : part_size;
                memcpy(buff, data + send_len, buff_len);
                send_len += buff_len;
                cm_uart_write(ctx->dev_num, buff, buff_len, 1000);
                while (cm_uart_is_sending(ctx->dev_num))
                    osDelay(1);
            }

            gc_try_free(gc_data);

            // 设置发送标志为成功
            if (ctx->auto_pull_flag != NULL)
            {
                osEventFlagsSet(ctx->auto_pull_flag, AUTO_PULL_FLAG_SEND_SUCCESS);
            }

            console_log("[uart %d] send thread, send end", ctx->dev_num);
        }
    }
}

/**
 * @brief 设置串口引脚复用功能为串口
 */
void set_uart_iomux_pin_func(uint8_t dev_num)
{

    if (dev_num == 0)
    {
        cm_iomux_set_pin_func(CM_IOMUX_PIN_18, CM_IOMUX_FUNC_FUNCTION1);
        cm_iomux_set_pin_func(CM_IOMUX_PIN_17, CM_IOMUX_FUNC_FUNCTION1);
    }

    if (dev_num == 1)
    {
        cm_iomux_set_pin_func(CM_IOMUX_PIN_28, CM_IOMUX_FUNC_FUNCTION1);
        cm_iomux_set_pin_func(CM_IOMUX_PIN_29, CM_IOMUX_FUNC_FUNCTION1);
    }

    if (dev_num == 2)
    {
        cm_iomux_set_pin_func(CM_IOMUX_PIN_38, CM_IOMUX_FUNC_FUNCTION2);
        cm_iomux_set_pin_func(CM_IOMUX_PIN_39, CM_IOMUX_FUNC_FUNCTION2);
    }
}

/**
 * @brief 如果串口编号是debug串口，则切换debug引脚打印的log到usb打印，掉电不保存配置，其他串口不做处理
 * @param[in] uart_num 串口编号
 */
static void set_virt_at_log2cat(uint32_t uart_num)
{
    if (uart_num != CM_UART_DEV_2)
    {
        return (void)0;
    }

    char operation[64] = { 0 };
    sprintf(operation, "%s\r\n", "AT+MCFG=log2cat,1");
    uint8_t rsp[128] = { 0 };
    int32_t rsp_len = 0;

    if (cm_virt_at_send_sync((const uint8_t*)operation, rsp, &rsp_len, 10) == 0)
    {
        cm_log_printf(0, "rsp=%s rsp_len=%d\n", rsp, rsp_len);
    }
    else
    {
        cm_log_printf(0, "ret != 0\n");
    }
}

/**
 * @brief 根据采集通道编号获取串口设备号
 *
 * @param collect_channel_num 采集通道号
 * @return uint8_t 设备号
 */
uint8_t get_dev_num_by_collect_channel_num(uint8_t collect_channel_num)
{
    if (collect_channel_num == 1)
        return CM_UART_DEV_1;

    if (collect_channel_num == 2)
        return CM_UART_DEV_0;

    if (collect_channel_num == 3)
        return CM_UART_DEV_2;

    return CM_UART_DEV_NUM;
}