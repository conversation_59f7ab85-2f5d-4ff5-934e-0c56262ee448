#include "common.h"
#include "dtu_channel.h"
#include "my_util.h"
#include "dtu_channel_mqtt.h"
#include "cm_sys.h"
#include "cm_ssl.h"
#include "cm_mem.h"
#include "cm_asocket.h"

// 函数向前声明
mqtt_channel_ctx_t* mqtt_channel_get_ctx(cm_mqtt_client_t* client);
/*!< connack*/
int mqtt_channel_connack_cb(cm_mqtt_client_t* client, int session, cm_mqtt_conn_state_e conn_res);
/*!< s->c publish*/
int mqtt_channel_publish_cb(cm_mqtt_client_t* client, unsigned short msgid, char* topic, int total_len, int payload_len, char* payload);
/*!< puback*/
int mqtt_channel_puback_cb(cm_mqtt_client_t* client, unsigned short msgid, char dup);
/*!< pubrec*/
int mqtt_channel_pubrec_cb(cm_mqtt_client_t* client, unsigned short msgid, char dup);
/*!< pubrel*/
int mqtt_channel_pubrel_cb(cm_mqtt_client_t* client, unsigned short msgid, char dup);
/*!< pubcomp*/
int mqtt_channel_pubcomp_cb(cm_mqtt_client_t* client, unsigned short msgid, char dup);
/*!< s->c suback*/
int mqtt_channel_suback_cb(cm_mqtt_client_t* client, unsigned short msgid, int count, int qos[]);
/*!< s->c unsuback*/
int mqtt_channel_unsuback_cb(cm_mqtt_client_t* client, unsigned short msgid);
/*!< s->c pinrsp*/
int mqtt_channel_pingresp_cb(cm_mqtt_client_t* client, int ret);
/*!< 发送超时*/
int mqtt_channel_timeout_cb(cm_mqtt_client_t* client, unsigned short msgid);
// 读取线程
static void mqtt_channel_read_thread(void* param);
// 发送线程
static void mqtt_channel_send_thread(void* param);

// 上下文列表，用于根据客户端查找上下文
dynamic_list_t* mqtt_ctx_list = NULL;

/**
 * @brief mqtt通道初始化
 *
 * @param param mqtt参数
 * @param id 通道id
 * @param callback 接收回调
 * @return mqtt_channel_ctx_t* mqtt通道上下文
 */
mqtt_channel_ctx_t* mqtt_channel_init(dtu_mqtt_param_t* param, uint8_t id, data_receive_callback_t callback)
{
    console_log("[mqtt channel %d] init start", id);

    mqtt_channel_ctx_t* ctx = (mqtt_channel_ctx_t*)cm_malloc(sizeof(mqtt_channel_ctx_t));
    if (ctx == NULL)
    {
        console_log("[mqtt channel %d] init failed, malloc ctx failed", id);
        return NULL;
    }

    ctx->id = id;
    ctx->param = param;
    ctx->callback = callback;
    ctx->message_pool = dynamic_list_init(sizeof(mqtt_message_t*));
    ctx->client = cm_mqtt_client_create();
    if (ctx->client == NULL)
    {
        console_log("[mqtt channel %d] init failed, client create failed", id);
        free_ptr(ctx);
        return NULL;
    }
    console_log("[mqtt channel %d] client create success", id);

    if (mqtt_ctx_list == NULL)
        mqtt_ctx_list = dynamic_list_init(sizeof(mqtt_channel_ctx_t*));
    dynamic_list_add(mqtt_ctx_list, ctx);

    // mqtt回调事件绑定
    cm_mqtt_client_cb_t* client_callback = (cm_mqtt_client_cb_t*)cm_malloc(sizeof(cm_mqtt_client_cb_t));
    if (client_callback == NULL)
    {
        console_log("[mqtt channel %d] init failed, malloc client callback failed", id);
        cm_mqtt_client_destroy(ctx->client);
        free_ptr(ctx);
        return NULL;
    }
    client_callback->connack_cb = mqtt_channel_connack_cb;
    client_callback->publish_cb = mqtt_channel_publish_cb;
    client_callback->puback_cb = mqtt_channel_puback_cb;
    client_callback->pubrec_cb = mqtt_channel_pubrec_cb;
    client_callback->pubrel_cb = mqtt_channel_pubrel_cb;
    client_callback->pubcomp_cb = mqtt_channel_pubcomp_cb;
    client_callback->suback_cb = mqtt_channel_suback_cb;
    client_callback->unsuback_cb = mqtt_channel_unsuback_cb;
    client_callback->pingresp_cb = mqtt_channel_pingresp_cb;
    client_callback->timeout_cb = mqtt_channel_timeout_cb;

    /* 设置client参数 */
    int version = 4;                                    // 版本3.1.1
    int pkt_timeout = 10;                               // 发送超时(秒)
    int reconn_times = 10;                              // 重连次数
    int reconn_cycle = 10;                              // 重连间隔(秒)
    int reconn_mode = 0;                                // 重连模式，以固定间隔尝试重连
    int retry_times = 3;                                // 重传三次
    int ping_cycle = ctx->param->heart_interval / 1000; // ping周期(秒)
    int dns_priority = 2;                               // MQTT dns解析ipv6优先
    cm_mqtt_client_set_opt(ctx->client, CM_MQTT_OPT_EVENT, (void*)client_callback);
    cm_mqtt_client_set_opt(ctx->client, CM_MQTT_OPT_VERSION, (void*)&version);
    cm_mqtt_client_set_opt(ctx->client, CM_MQTT_OPT_PKT_TIMEOUT, (void*)&pkt_timeout);
    cm_mqtt_client_set_opt(ctx->client, CM_MQTT_OPT_RETRY_TIMES, (void*)&retry_times);
    cm_mqtt_client_set_opt(ctx->client, CM_MQTT_OPT_RECONN_MODE, (void*)&reconn_mode);
    cm_mqtt_client_set_opt(ctx->client, CM_MQTT_OPT_RECONN_TIMES, (void*)&reconn_times);
    cm_mqtt_client_set_opt(ctx->client, CM_MQTT_OPT_RECONN_CYCLE, (void*)&reconn_cycle);
    cm_mqtt_client_set_opt(ctx->client, CM_MQTT_OPT_PING_CYCLE, (void*)&ping_cycle);
    cm_mqtt_client_set_opt(ctx->client, CM_MQTT_OPT_DNS_PRIORITY, (void*)&dns_priority);

    // 设置SSL参数
    if (param->ssl_enable == 1 && not_empty(param->ssl_ca_cert))
    {
        int ssl_id = id;    //SSL通道
        int verify = 0;     //验证类型，0是无证书验证
        int negotime = 60;  //握手超时(s)
        int ssl_enable = 1; //开启MQTTS
        cm_ssl_setopt(ssl_id, CM_SSL_PARAM_VERIFY, &verify);
        cm_ssl_setopt(ssl_id, CM_SSL_PARAM_NEGOTIME, &negotime);
        cm_mqtt_client_set_opt(ctx->client, CM_MQTT_OPT_SSL_ID, &ssl_id);         //将SSL通道注册到mqtt实例中
        cm_mqtt_client_set_opt(ctx->client, CM_MQTT_OPT_SSL_ENABLE, &ssl_enable); //该实例开启MQTTS
    }

    console_log("[mqtt channel %d] client option init success", id);

    /* 配置连接参数，对于字符串参数，内部仅保留指针，不分配空间 */
    cm_mqtt_connect_options_t* conn_options = (cm_mqtt_connect_options_t*)cm_malloc(sizeof(cm_mqtt_connect_options_t));
    if (conn_options == NULL)
    {
        console_log("[mqtt channel %d] init failed, malloc connect options failed", id);
        cm_mqtt_client_destroy(ctx->client);
        free_ptr(client_callback);
        free_ptr(ctx);
        return NULL;
    }
    conn_options->hostname = (const char*)param->host;
    conn_options->hostport = (unsigned short)param->port;
    conn_options->clientid = if_empty(param->client_id, render_template("${imei}"));
    conn_options->username = (const char*)param->username;
    conn_options->password = (const char*)param->password;
    conn_options->keepalive = (unsigned short)ping_cycle;
    conn_options->clean_session = (char)param->clean_session;
    conn_options->will_flag = param->last_will_enable;
    if (param->last_will_enable == 1)
    {
        conn_options->will_topic = (const char*)param->last_will_topic;
        conn_options->will_message = (const char*)param->last_will_content;
        conn_options->will_message_len = (int)strlen(param->last_will_content);
        conn_options->will_qos = (char)param->last_will_qos;
        conn_options->will_retain = (char)param->last_will_retain;
    }
    else
    {
        conn_options->will_topic = NULL;
        conn_options->will_message = NULL;
        conn_options->will_message_len = 0;
    }
    ctx->connect_options = conn_options;

    console_log("[mqtt channel %d] connect options init success", id);

    // 连接
    cm_mqtt_client_connect(ctx->client, ctx->connect_options);

    // 开启读取线程
    ctx->read_queue_id = osMessageQueueNew(10, sizeof(comm_msg_t*), NULL);
    osThreadAttr_t read_thread_attr = { 0 };
    char read_thread_name[30];
    sprintf(read_thread_name, "mqtt channel %d read thread", ctx->id);
    read_thread_attr.name = read_thread_name;
    read_thread_attr.stack_size = 2048;
    read_thread_attr.priority = osPriorityNormal;
    ctx->read_thread_id = osThreadNew((osThreadFunc_t)mqtt_channel_read_thread, ctx, &read_thread_attr);

    // 开启发送线程
    ctx->send_queue_id = osMessageQueueNew(10, sizeof(comm_gc_msg_t*), NULL);
    osThreadAttr_t send_thread_attr = { 0 };
    char send_thread_name[30];
    sprintf(send_thread_name, "mqtt channel %d send thead", ctx->id);
    send_thread_attr.name = send_thread_name;
    send_thread_attr.stack_size = 1024;
    send_thread_attr.priority = osPriorityNormal;
    ctx->send_thread_id = osThreadNew((osThreadFunc_t)mqtt_channel_send_thread, ctx, &send_thread_attr);

    console_log("[mqtt channel %d] init end", id);

    return ctx;
}

/**
 * @brief 根据mqtt客户端获取上下文
 *
 * @param client
 *
 * @return
 */
mqtt_channel_ctx_t* mqtt_channel_get_ctx(cm_mqtt_client_t* client)
{
    if (mqtt_ctx_list == NULL)
        return NULL;

    mqtt_channel_ctx_t** arr = (mqtt_channel_ctx_t**)mqtt_ctx_list->data;
    for (size_t i = 0; i < mqtt_ctx_list->size; i++)
    {
        if (arr[i]->client == client)
            return arr[i];
    }

    return NULL;
}

/**
 * @brief mqtt连接成功回调
 */
void mqtt_channel_connect_success(mqtt_channel_ctx_t* ctx)
{
    // 取消订阅之前订阅的topic
    linklist_t* list = cm_mqtt_client_get_sub_topics(ctx->client);
    if (list != NULL && list->count > 0)
    {
        linklist_element_t* element = NULL;
        cm_mqtt_topic_t* sub_topic = NULL;
        while ((element = linklist_next_element(list, &element)) != NULL)
        {
            sub_topic = (cm_mqtt_topic_t*)element->content;
            if (sub_topic->state != CM_MQTT_TOPIC_SUBSCRIBED)
            {
                continue;
            }
            char* topics[1] = { sub_topic->topic };
            cm_mqtt_client_unsubscribe(ctx->client, (const char**)topics, 1);
        }
    }

    // 订阅topic
    if (not_empty(ctx->param->sub_topic))
    {
        char* topic = render_template(ctx->param->sub_topic);
        if (topic == NULL)
        {
            console_log("[mqtt channel %d] sub topic render failed", ctx->id);
            return;
        }

        char* topics[1] = { topic };
        char qos[1] = { ctx->param->sub_qos };
        cm_mqtt_client_subscribe(ctx->client, (const char**)topics, (char*)qos, 1);
    }
}

/**
 * @brief 连接状态回调
 *
 * @param client mqtt客户端
 * @param session 会话标志
 * @param conn_res 连接状态
 */
int mqtt_channel_connack_cb(cm_mqtt_client_t* client, int session, cm_mqtt_conn_state_e conn_res)
{
    mqtt_channel_ctx_t* ctx = mqtt_channel_get_ctx(client);
    if (ctx != NULL)
    {
        console_log("[mqtt channel %d] conn_ack: conn_res=%d", ctx->id, conn_res);
        ctx->conn_state = conn_res;

        if (conn_res == CM_MQTT_CONN_STATE_SUCCESS)
        {
            mqtt_channel_connect_success(ctx);
        }
    }
    return 0;
}

/**
 * @brief server->client发布消息回调
 *
 * @param client mqtt客户端
 * @param msgid 消息id
 * @param topic 主题
 * @param total_len 消息总长度
 * @param payload_len 消息有效长度
 * @param payload 消息有效内容
 */
int mqtt_channel_publish_cb(cm_mqtt_client_t* client, unsigned short msgid, char* topic, int total_len, int payload_len, char* payload)
{
    mqtt_channel_ctx_t* ctx = mqtt_channel_get_ctx(client);
    if (ctx != NULL)
    {
        console_log("[mqtt channel %d] server publish: topic=%s payload_len=%d total_len=%d", ctx->id, topic, payload_len, total_len);

        // 如果有效长度不等于总长度，则按分包处理
        if (total_len != payload_len)
        {
            // 查询当前消息
            mqtt_message_t* current_msg = NULL;
            for (size_t i = 0; i < ctx->message_pool->size; i++)
            {
                mqtt_message_t* msg = (mqtt_message_t*)ctx->message_pool->data[i];
                if (msg->msgid == msgid)
                {
                    current_msg = msg;
                    break;
                }
            }

            // 如果不存在当前消息，则创建
            if (current_msg == NULL)
            {
                current_msg = (mqtt_message_t*)cm_malloc(sizeof(mqtt_message_t)); 
                if (current_msg == NULL)
                {
                    console_log("[mqtt channel %d] server publish end, malloc msg failed", ctx->id);
                    return 0;
                }
                current_msg->msgid = msgid;
                current_msg->payload_len = payload_len;
                current_msg->payload = copy_str_by_len(payload, payload_len);
                dynamic_list_add(ctx->message_pool, current_msg);
            }
            // 否则，追加数据
            else 
            {
                current_msg->payload = cm_realloc(current_msg->payload, current_msg->payload_len + payload_len + 1);
                memcpy(current_msg->payload + current_msg->payload_len, payload, payload_len);
                current_msg->payload_len += payload_len;
                current_msg->payload[current_msg->payload_len] = '\0';
            }

            // 如果所有分包都接收完毕，则处理数据
            if (current_msg->payload_len == total_len)
            { 
                comm_msg_t* msg = (comm_msg_t*)cm_malloc(sizeof(comm_msg_t));
                if (msg == NULL)
                {
                    console_log("[mqtt channel %d] server publish end, malloc msg failed", ctx->id);
                    return 0;
                }
                msg->data = (uint8_t*)current_msg->payload;
                msg->len = current_msg->payload_len;
                osMessageQueuePut(ctx->read_queue_id, &msg, 0, 0);

                dynamic_list_remove(ctx->message_pool, current_msg);
                free_ptr(current_msg);
            }
            
            return 0;
        }
        
        comm_msg_t* msg = (comm_msg_t*)cm_malloc(sizeof(comm_msg_t));
        if (msg == NULL)
        {
            console_log("[mqtt channel %d] server publish end, malloc msg failed", ctx->id);
            return 0;
        }
        msg->data = (uint8_t*)copy_str_by_len(payload, payload_len);
        msg->len = payload_len;
        osMessageQueuePut(ctx->read_queue_id, &msg, 0, 0);
    }
    return 0;
}

/**
 * @brief client->server发布消息ack回调
 *
 * @param client mqtt客户端
 * @param msgid 消息id
 * @param dup dup标志
 */
int mqtt_channel_puback_cb(cm_mqtt_client_t* client, unsigned short msgid, char dup)
{
    mqtt_channel_ctx_t* ctx = mqtt_channel_get_ctx(client);
    if (ctx != NULL)
    {
        console_log("[mqtt channel %d] client pub_ack: msgid=%d dup=%d", ctx->id, msgid, dup);
    }
    return 0;
}

/**
 * @brief client->server发布消息rec回调
 *
 * @param client mqtt客户端
 * @param msgid 消息id
 * @param dup dup标志
 */
int mqtt_channel_pubrec_cb(cm_mqtt_client_t* client, unsigned short msgid, char dup)
{
    mqtt_channel_ctx_t* ctx = mqtt_channel_get_ctx(client);
    if (ctx != NULL)
    {
        console_log("[mqtt channel %d] client pub_rec: msgid=%d dup=%d", ctx->id, msgid, dup);
    }
    return 0;
}

/**
 * @brief client->server发布消息rel回调
 *
 * @param client mqtt客户端
 * @param msgid 消息id
 * @param dup dup标志
 */
int mqtt_channel_pubrel_cb(cm_mqtt_client_t* client, unsigned short msgid, char dup)
{
    mqtt_channel_ctx_t* ctx = mqtt_channel_get_ctx(client);
    if (ctx != NULL)
    {
        console_log("[mqtt channel %d] client pub_rel: msgid=%d dup=%d", ctx->id, msgid, dup);
    }
    return 0;
}

/**
 * @brief client->server发布消息comp回调
 *
 * @param client mqtt客户端
 * @param msgid 消息id
 * @param dup dup标志
 */
int mqtt_channel_pubcomp_cb(cm_mqtt_client_t* client, unsigned short msgid, char dup)
{
    mqtt_channel_ctx_t* ctx = mqtt_channel_get_ctx(client);
    if (ctx != NULL)
    {
        console_log("[mqtt channel %d] client pub_comp: msgid=%d dup=%d", ctx->id, msgid, dup);
    }
    return 0;
}

/**
 * @brief server->client订阅ack回调
 *
 * @param client mqtt客户端
 * @param msgid 消息id
 * @param count 数量
 * @param qos 消息质量
 */
int mqtt_channel_suback_cb(cm_mqtt_client_t* client, unsigned short msgid, int count, int qos[])
{
    mqtt_channel_ctx_t* ctx = mqtt_channel_get_ctx(client);
    if (ctx != NULL)
    {
        console_log("[mqtt channel %d] client sub_ack: msgid=%d count=%d", ctx->id, msgid, count);
    }
    return 0;
}

/**
 * @brief server->client取消订阅ack回调
 *
 * @param client mqtt客户端
 * @param msgid 消息id
 */
int mqtt_channel_unsuback_cb(cm_mqtt_client_t* client, unsigned short msgid)
{
    mqtt_channel_ctx_t* ctx = mqtt_channel_get_ctx(client);
    if (ctx != NULL)
    {
        console_log("[mqtt channel %d] server sub_ack: msgid=%d", ctx->id);
    }
    return 0;
}

/**
 * @brief mqtt心跳回调
 *
 * @param client mqtt客户端
 * @param ret 消息状态 0:ping成功 1:ping超时
 */
int mqtt_channel_pingresp_cb(cm_mqtt_client_t* client, int ret)
{
    mqtt_channel_ctx_t* ctx = mqtt_channel_get_ctx(client);
    if (ctx != NULL)
    {
        console_log("[mqtt channel %d] server pin_resp: ret=%d", ctx->id, ret);
    }
    return 0;
}

/**
 * @brief mqtt消息超时回调，包括publish/subscribe/unsubscribe等
 *
 * @param client mqtt客户端
 * @param msgid 消息id
 */
int mqtt_channel_timeout_cb(cm_mqtt_client_t* client, unsigned short msgid)
{
    mqtt_channel_ctx_t* ctx = mqtt_channel_get_ctx(client);
    if (ctx != NULL)
    {
        console_log("[mqtt channel %d] client timeout: msgid=%d ", ctx->id, msgid);
    }
    return 0;
}

// 读取线程
static void mqtt_channel_read_thread(void* param)
{
    mqtt_channel_ctx_t* ctx = (mqtt_channel_ctx_t*)param;

    console_log("[mqtt channel %d] read thread start", ctx->id);

    while (TRUE)
    {
        comm_msg_t* msg = NULL;
        if (osMessageQueueGet(ctx->read_queue_id, &msg, 0, osWaitForever) == osOK)
        {
            console_log("[mqtt channel %d] read thread, read start", ctx->id);
            ctx->callback(ctx, msg->data, msg->len);
            free_ptr(msg);
            console_log("[mqtt channel %d] read thread, read end", ctx->id);
        }
    }
}

// 发送线程
static void mqtt_channel_send_thread(void* param)
{
    mqtt_channel_ctx_t* ctx = (mqtt_channel_ctx_t*)param;

    console_log("[mqtt channel %d] send thread start", ctx->id);

    while (TRUE)
    {
        comm_gc_msg_t* msg = NULL;
        if (osMessageQueueGet(ctx->send_queue_id, &msg, 0, osWaitForever) == osOK)
        {
            console_log("[mqtt channel %d] send thread, send start", ctx->id);

            gc_data_t* gc_data = msg->data;
            size_t len = msg->len;

            // 释放msg
            free_ptr(msg);

            if (ctx->conn_state != CM_MQTT_CONN_STATE_SUCCESS)
            {
                console_log("[mqtt channel %d] send thread, send end, not connected", ctx->id);
                gc_try_free(gc_data);
                continue;
            }

            if (is_empty(ctx->param->pub_topic))
            {
                console_log("[mqtt channel %d] send thread, send end, no pub topic", ctx->id);
                gc_try_free(gc_data);
                continue;
            }

            char* topic = render_template(ctx->param->pub_topic);
            if (topic == NULL)
            {
                console_log("[mqtt channel %d] send thread, send end, render topic failed", ctx->id);
                gc_try_free(gc_data);
                continue;
            }

            uint8_t publish_flags = 0;
            publish_flags |= (((ctx->param->pub_qos & 0x03) << 1) & 0x06);
            publish_flags |= (ctx->param->pub_retain & 0x01);
            cm_mqtt_client_publish(ctx->client, (const char*)topic, (const char*)gc_data->data, len, publish_flags);

            // 释放内存 
            gc_try_free(gc_data);
            free_ptr(topic);

            console_log("[mqtt channel %d] send thread, send end", ctx->id);
        }
    }
}

/**
 * @brief mqtt通道发送
 *
 * @param ctx mqtt通道上下文
 * @param data 数据
 * @param len 数据长度
 */
COMMON_RES_T mqtt_channel_send(mqtt_channel_ctx_t* ctx, gc_data_t* data, size_t len)
{
    console_log("[mqtt channel %d] send start", ctx->id);

    comm_gc_msg_t* msg = (comm_gc_msg_t*)cm_malloc(sizeof(comm_gc_msg_t));
    if (msg == NULL)
    {
        console_log("[mqtt channel %d] send malloc msg error", ctx->id);
        return RES_ERROR;
    }

    msg->data = data;
    msg->len = len;

    osMessageQueuePut(ctx->send_queue_id, &msg, 0, 0);

    console_log("[mqtt channel %d] send end", ctx->id);

    return RES_SUCCESS;
}

/**
 * @brief mqtt通道关闭
 *
 * @param ctx mqtt通道上下文
 */
void mqtt_channel_close(mqtt_channel_ctx_t* ctx)
{
    // 断开连接
    cm_mqtt_client_disconnect(ctx->client);

    // 停止发送线程
    osThreadTerminate(ctx->send_thread_id);
    ctx->send_thread_id = NULL;

    // 停止读取线程
    osThreadTerminate(ctx->read_thread_id);
    ctx->read_thread_id = NULL;

    // 删除消息队列
    osMessageQueueDelete(ctx->send_queue_id);
    ctx->send_queue_id = NULL;
    osMessageQueueDelete(ctx->read_queue_id);
    ctx->read_queue_id = NULL;

    free_ptr(ctx->connect_options);
    ctx->connect_options = NULL;
    free_ptr(ctx->client_callback);
    ctx->client_callback = NULL;

    if (mqtt_ctx_list != NULL)
        dynamic_list_remove(mqtt_ctx_list, ctx);

    free_ptr(ctx);
}