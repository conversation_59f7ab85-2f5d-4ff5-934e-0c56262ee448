#include "common.h"
#include "dtu_channel.h"
#include "my_util.h"
#include "dtu_channel_http.h"
#include "cm_sys.h"
#include "cm_ssl.h"
#include "cm_mem.h"
#include "cm_http.h"

// 发送线程
static void http_channel_send_thread(void* param);

/**
 * @brief http通道初始化
 *
 * @param param http参数
 * @param id 通道id
 * @param callback 接收回调
 * @return http_channel_ctx_t* http通道上下文
 */
http_channel_ctx_t* http_channel_init(dtu_http_param_t* param, uint8_t id, data_receive_callback_t callback)
{
    console_log("[http channel %d] init start", id);

    http_channel_ctx_t* ctx = (http_channel_ctx_t*)cm_malloc(sizeof(http_channel_ctx_t));
    if (ctx == NULL)
    {
        console_log("[http channel %d] init failed, malloc ctx failed", id);
        return NULL;
    }

    // 创建客户端
    cm_httpclient_handle_t client = NULL;
    cm_httpclient_ret_code_e ret = CM_HTTP_RET_CODE_UNKNOWN_ERROR;
    ret = cm_httpclient_create((const u_int8_t*)param->server, NULL, &client);
    if (CM_HTTP_RET_CODE_OK != ret || NULL == client)
    {
        console_log("[http channel %d] init failed, http client create failed", id);
        free_ptr(ctx);
        return NULL;
    }

    // 设置参数
    cm_httpclient_cfg_t client_cfg;
    client_cfg.ssl_enable = param->ssl_enable;                    // 使用SSL，即HTTPS连接方式。使用HTTP方式时该值为false
    client_cfg.ssl_id = id;                                       // 设置SSL索引号
    client_cfg.cid = 0;                                           // 设置PDP索引号，目前不支持该项设置，设置任意值即可
    client_cfg.conn_timeout = param->timeout / 1000;              // 设置连接超时时间
    client_cfg.rsp_timeout = HTTPCLIENT_WAITRSP_TIMEOUT_DEFAULT;  // 设置响应超时时间
    client_cfg.dns_priority = 1;                                  // 设置DNS解析优先级，ipv6解析优先
    ret = cm_httpclient_set_cfg(client, client_cfg);              // 客户端参数设置

    if (CM_HTTP_RET_CODE_OK != ret || NULL == client)
    {
        console_log("[http channel %d] init failed, http client set cfg failed", id);
        free_ptr(ctx);
        cm_httpclient_delete(client);
        return NULL;
    }

    // 设置ssl证书
    if (param->ssl_enable)
    {
        int verify = 0;
        cm_ssl_setopt(id, CM_SSL_PARAM_VERIFY, &verify);              // 设置SSL验证方式
        cm_ssl_setopt(id, CM_SSL_PARAM_CA_CERT, param->ssl_ca_cert);  // 设置CA证书
    }

    ctx->id = id;
    ctx->param = param;
    ctx->client = client;
    ctx->callback = callback;

    // 开启发送线程
    ctx->send_queue_id = osMessageQueueNew(10, sizeof(comm_gc_msg_t*), NULL);
    osThreadAttr_t send_thread_attr = { 0 };
    char send_thread_name[30];
    sprintf(send_thread_name, "http channel %d send thread", ctx->id);
    send_thread_attr.name = send_thread_name;
    send_thread_attr.stack_size = 2048;
    send_thread_attr.priority = osPriorityNormal;
    ctx->send_thread_id = osThreadNew((osThreadFunc_t)http_channel_send_thread, ctx, &send_thread_attr);

    console_log("[http channel %d] init end", id);

    return ctx;
}

// 发送线程
static void http_channel_send_thread(void* param)
{
    http_channel_ctx_t* ctx = (http_channel_ctx_t*)param;

    console_log("[http channel %d] send thread start", ctx->id);

    while (TRUE)
    {
        comm_gc_msg_t* msg = NULL;
        if (osMessageQueueGet(ctx->send_queue_id, &msg, 0, osWaitForever) == osOK)
        {
            console_log("[http channel %d] send thread, send start", ctx->id);

            gc_data_t* gc_data = msg->data;
            size_t len = msg->len; 
            
            // 释放msg
            free_ptr(msg);

            cm_httpclient_sync_param_t req = { .method = HTTPCLIENT_REQUEST_GET };
            // 请求方法
            if (strcmp(ctx->param->method, "POST") == 0 || strcmp(ctx->param->method, "post") == 0)
            {
                req.method = HTTPCLIENT_REQUEST_POST;
            }

            // 请求路径
            char* path = render_template(ctx->param->path);

            // 数据在url
            if (strcmp(ctx->param->request_type, "inUrl") == 0)
            {
                // 替换${data}占位符
                if (strstr(path, "${data}") != NULL)
                {
                    char* encode_data = (char*)cm_httpclient_uri_encode_component((const uint8_t*)gc_data->data, len);
                    char* nwe_path = str_replace(path, "${data}", encode_data);
                    free_ptr(encode_data);
                    free_ptr(path);
                    path = nwe_path;
                }

                // 替换${dataHex}占位符
                if (strstr(path, "${dataHex}") != NULL)
                {
                    char* encode_data = bytes_to_hex((uint8_t*)gc_data->data, len);
                    char* nwe_path = str_replace(path, "${dataHex}", encode_data);
                    free_ptr(encode_data);
                    free_ptr(path);
                    path = nwe_path;
                }

                req.path = (uint8_t*)path;
                req.content = NULL;
                req.content_length = 0;
            }
            // 数据在body
            else
            {
                req.path = (uint8_t*)path;
                req.content = gc_data->data;
                req.content_length = len;

                // 设置content-type
                if (not_empty(ctx->param->content_type))
                {
                    char content_type[128] = { 0 };
                    sprintf(content_type, "Content-Type: %s", ctx->param->content_type);
                    cm_httpclient_custom_header_set(ctx->client, (uint8_t*)content_type, strlen(content_type));
                }
            }

            // 设置header
            if (not_empty(ctx->param->headers))
            {
                cm_httpclient_specific_header_set(ctx->client, (uint8_t*)ctx->param->headers, strlen(ctx->param->headers));
            }

            // 执行请求
            cm_httpclient_sync_response_t response = {};
            cm_httpclient_ret_code_e ret = cm_httpclient_sync_request(ctx->client, req, &response);

            // 请求失败
            if (CM_HTTP_RET_CODE_OK != ret)
            {
                console_log("[http channel %d] send thread, request failed, ret=%d", ctx->id, ret);
                // 释放内存
                free_ptr(path);
                gc_try_free(gc_data);
                cm_httpclient_sync_free_data(ctx->client);
                continue;
            }

            console_log("[http channel %d] send thread, request success, status=%d, response_len=%d", ctx->id, response.response_code, response.response_content_len);

            // 如果有响应内容
            if (response.response_content_len > 0)
            {
                char* res_data = copy_str_by_len((char*)response.response_content, response.response_content_len);
                if (res_data == NULL)
                {
                    console_log("[http channel %d] send thread, read failed, malloc response failed", ctx->id);
                }
                ctx->callback(ctx, (uint8_t*)res_data, response.response_content_len);
            }

            // 释放内存
            free_ptr(path);
            gc_try_free(gc_data);
            cm_httpclient_custom_header_free(ctx->client);
            cm_httpclient_specific_header_free(ctx->client);
            cm_httpclient_sync_free_data(ctx->client);

            console_log("[http channel %d] send thread, send end", ctx->id);
        }
    }
}

/**
 * @brief http通道发送
 *
 * @param ctx http通道上下文
 * @param data 数据
 * @param len 数据长度
 */
COMMON_RES_T http_channel_send(http_channel_ctx_t* ctx, gc_data_t* data, size_t len)
{
    console_log("[http channel %d] send start", ctx->id);

    comm_gc_msg_t* msg = (comm_gc_msg_t*)cm_malloc(sizeof(comm_gc_msg_t));
    if (msg == NULL)
    {
        console_log("[http channel %d] send malloc msg error", ctx->id);
        return RES_ERROR;
    }

    msg->data = data;
    msg->len = len;

    osMessageQueuePut(ctx->send_queue_id, &msg, 0, 0);

    console_log("[http channel %d] send end", ctx->id);

    return RES_SUCCESS;
}

/**
 * @brief http通道关闭
 *
 * @param ctx http通道上下文
 */
void http_channel_close(http_channel_ctx_t* ctx)
{
    // 断开连接
    cm_httpclient_terminate(ctx->client);
    cm_httpclient_delete(ctx->client);

    // 停止发送线程
    osThreadTerminate(ctx->send_thread_id);
    ctx->send_thread_id = NULL;

    // 删除消息队列
    osMessageQueueDelete(ctx->send_queue_id);
    ctx->send_queue_id = NULL;

    free_ptr(ctx);
}