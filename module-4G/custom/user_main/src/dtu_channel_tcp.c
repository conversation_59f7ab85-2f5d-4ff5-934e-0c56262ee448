#include "common.h"
#include "dtu_channel.h"
#include "my_util.h"
#include "dtu_channel_tcp.h"
#include "cm_sys.h"
#include "cm_ssl.h"
#include "cm_mem.h"
#include "cm_asocket.h"
#include "lwip/sockets.h"
#include "ssl.h"
#include "cm_modem.h"
#include "cm_sim.h"

// 函数向前声明
static void tcp_channel_register(cm_eloop_event_handle_t event, void* cb_param);
static void tcp_channel_asocket_event(int sock, cm_asocket_event_e event, void* user_param);
static void tcp_channel_ssl_connect(cm_eloop_event_handle_t event, void* cb_param);
static void socket_channel_send_register_content(tcp_channel_ctx_t* ctx);
static void tcp_channel_conn_thread(void* param);
static void socket_channel_read_thread(void* param);
static void tcp_channel_pack_timer(void* param);
static void tcp_channel_heart_thread(void* param);
static void socket_channel_consume_thread(void* param);
static void socket_channel_send_thread(void* param);

/**
 * @brief tcp通道初始化
 *
 * @param param socket参数
 * @param id 通道id
 * @param callback 接收回调
 * @return tcp_channel_ctx_t* tcp通道上下文
 */
tcp_channel_ctx_t* tcp_channel_init(dtu_socket_param_t* param, uint8_t id, data_receive_callback_t callback)
{
    console_log("[tcp channel %d] init start", id);

    tcp_channel_ctx_t* ctx = (tcp_channel_ctx_t*)cm_malloc(sizeof(tcp_channel_ctx_t));
    if (ctx == NULL)
    {
        console_log("[tcp channel %d] init failed, malloc ctx failed", id);
        return NULL;
    }

    ctx->param = param;
    ctx->id = id;
    ctx->connect_status = 0;
    ctx->callback = callback;
    cm_eloop_event_handle_t socket_register_event = cm_eloop_register_event(cm_asocket_eloop(), tcp_channel_register, ctx);
    cm_eloop_post_event(socket_register_event);

    console_log("[tcp channel %d] init end", id);
    return ctx;
}

/**
 * @brief socket注册EventLoop回调函数
 * @param event EventLoop事件
 * @param cb_param 回调函数参数
 */
static void tcp_channel_register(cm_eloop_event_handle_t event, void* cb_param)
{
    tcp_channel_ctx_t* ctx = (tcp_channel_ctx_t*)cb_param;

    console_log("[tcp channel %d] socket register start", ctx->id);

    /* 注销Event */
    cm_eloop_unregister_event(event);

    // 开启socket连接线程
    osThreadAttr_t connect_thread_attr = { 0 };
    char conn_thread_name[30];
    sprintf(conn_thread_name, "tcp channel %d connect", ctx->id);
    connect_thread_attr.name = conn_thread_name;
    connect_thread_attr.stack_size = 1024;
    connect_thread_attr.priority = osPriorityNormal;
    ctx->connect_thread_id = osThreadNew((osThreadFunc_t)tcp_channel_conn_thread, ctx, &connect_thread_attr);

    // 开启socket读线程
    ctx->read_thread_sem = osSemaphoreNew(1, 0, NULL);
    osThreadAttr_t read_thread_attr = { 0 };
    char read_thread_name[30];
    sprintf(read_thread_name, "tcp channel %d read", ctx->id);
    read_thread_attr.name = read_thread_name;
    read_thread_attr.stack_size = 1024;
    read_thread_attr.priority = osPriorityNormal;
    ctx->read_thread_id = osThreadNew((osThreadFunc_t)socket_channel_read_thread, ctx, &read_thread_attr);

    // 创建socket分包定时器
    ctx->pack_timer_id = osTimerNew((osTimerFunc_t)tcp_channel_pack_timer, osTimerOnce, ctx, NULL);

    // 创建发送队列
    ctx->send_queue_id = osMessageQueueNew(10, sizeof(comm_gc_msg_t*), NULL);

    // 创建发送线程
    osThreadAttr_t send_thread_attr = { 0 };
    char send_thread_name[30];
    sprintf(send_thread_name, "tcp channel %d send", ctx->id);
    send_thread_attr.name = send_thread_name;
    send_thread_attr.stack_size = 1024;
    send_thread_attr.priority = osPriorityNormal;
    ctx->read_thread_id = osThreadNew((osThreadFunc_t)socket_channel_send_thread, ctx, &send_thread_attr);

    // 创建消费队列
    ctx->consume_queue_id = osMessageQueueNew(10, sizeof(comm_msg_t*), NULL);

    // 创建消费线程
    osThreadAttr_t consume_thread_attr = { 0 };
    char consume_thread_name[30];
    sprintf(consume_thread_name, "tcp channel %d consume", ctx->id);
    consume_thread_attr.name = consume_thread_name;
    consume_thread_attr.stack_size = 2048;
    consume_thread_attr.priority = osPriorityNormal;
    ctx->consume_thread_id = osThreadNew((osThreadFunc_t)socket_channel_consume_thread, ctx, &consume_thread_attr);

    // 开启socket心跳线程
    if (ctx->param->heart_interval > 0 && not_empty(ctx->param->heart_content))
    {
        osThreadAttr_t heart_thread_attr = { 0 };
        char heart_thread_name[30];
        sprintf(heart_thread_name, "tcp channel %d heart", ctx->id);
        heart_thread_attr.name = heart_thread_name;
        heart_thread_attr.stack_size = 2048;
        heart_thread_attr.priority = osPriorityNormal;
        ctx->read_thread_id = osThreadNew((osThreadFunc_t)tcp_channel_heart_thread, ctx, &heart_thread_attr);
    }

    console_log("[tcp channel %d] socket register end", ctx->id);
}

/**
 * @brief socket事件处理函数
 *
 * @param sock socket句柄
 * @param event socket事件
 * @param user_param 用户参数
 */
static void tcp_channel_asocket_event(int sock, cm_asocket_event_e event, void* user_param)
{
    tcp_channel_ctx_t* ctx = (tcp_channel_ctx_t*)user_param;
    // 连接成功
    if (event == CM_ASOCKET_EV_CONNECT_OK)
    {
        console_log("[tcp channel %d] connect ok", ctx->id);
        if (ctx->param->ssl_enable)
        {
            ctx->connect_status = 2;
            cm_eloop_event_handle_t ssl_connect_event = cm_eloop_register_event(cm_asocket_eloop(), tcp_channel_ssl_connect, ctx);
            cm_eloop_post_event(ssl_connect_event);
        }
        else
        {
            ctx->connect_status = 3;

            socket_channel_send_register_content(ctx);
        }
    }
    // 连接失败
    else if (event == CM_ASOCKET_EV_CONNECT_FAIL)
    {
        console_log("[tcp channel %d] connect failed", ctx->id);
        ctx->connect_status = 0;
    }
    // 接收到数据
    else if (event == CM_ASOCKET_EV_RECV_IND)
    {
        // SSL握手阶段会上报读取事件，握手阶段不可进行数据读取
        if (2 == ctx->connect_status)
        {
            console_log("[tcp channel %d] ssl shaking hands", ctx->id);
            return;
        }

        console_log("[tcp channel %d] receive ind", ctx->id);

        if (ctx->connect_status != 3)
        {
            console_log("[tcp channel %d] receive ind, socket is not connected", ctx->id);
            return;
        }

        // 释放读取信号
        osSemaphoreRelease(ctx->read_thread_sem);
    }
    // 连接异常
    else if (event == CM_ASOCKET_EV_ERROR_IND)
    {
        /* 获取socket错误码 */
        int sock_error = 0;
        socklen_t opt_len = sizeof(sock_error);
        cm_asocket_getsockopt(sock, SOL_SOCKET, SO_ERROR, &sock_error, &opt_len);

        console_log("[tcp channel %d] error ind, code is %d", ctx->id, sock_error);

        // 断开连接
        if (ECONNABORTED == sock_error)
        {
            console_log("[tcp channel %d] error_ind, Connection aborted", ctx->id);
        }
        // 连接重置
        else if (ECONNRESET == sock_error)
        {
            console_log("[tcp channel %d] error_ind, Connection reset", ctx->id);
        }
        // 连接关闭
        else if (ENOTCONN == sock_error)
        {
            console_log("[tcp channel %d] error_ind, Connection closed", ctx->id);
        }

        ctx->connect_status = 0;

        // 尝试释放资源
        if (ctx->sock != -1)
        {
            cm_asocket_close(ctx->sock);
        }
    }
}

/**
 * @brief 开始SSL连接
 */
static void tcp_channel_ssl_connect(cm_eloop_event_handle_t event, void* cb_param)
{
    tcp_channel_ctx_t* ctx = (tcp_channel_ctx_t*)cb_param;

    console_log("[tcp channel %d] ssl connect start", ctx->id);

    /* 注销Event */
    cm_eloop_unregister_event(event);

    /* 事先需要设置SSL参数，先选择使用通道，再设置该通道的验证类型，验证所需的证书 */
    int ssl_id = ctx->id;    // SSL通道
    int verify = 0;          // 证书验证方式
    uint16_t negotime = 60;  // 握手超时(s)
    uint8_t session = 1;
    uint8_t sni = 0;
    uint8_t version = 255;
    uint16_t cipher_suite = 0x0000;
    uint8_t ignorstamp = 0;
    uint8_t ignorverify = 0;
    cm_ssl_setopt(ssl_id, CM_SSL_PARAM_CA_CERT, ctx->param->ssl_ca_cert);
    cm_ssl_setopt(ssl_id, CM_SSL_PARAM_CLI_CERT, "");
    cm_ssl_setopt(ssl_id, CM_SSL_PARAM_CLI_KEY, "");
    cm_ssl_setopt(ssl_id, CM_SSL_PARAM_NEGOTIME, &negotime);
    cm_ssl_setopt(ssl_id, CM_SSL_PARAM_SESSION, &session);
    cm_ssl_setopt(ssl_id, CM_SSL_PARAM_SNI, &sni);
    cm_ssl_setopt(ssl_id, CM_SSL_PARAM_VERSION, &version);
    cm_ssl_setopt(ssl_id, CM_SSL_PARAM_CIPHER_SUITE, &cipher_suite);
    cm_ssl_setopt(ssl_id, CM_SSL_PARAM_IGNORESTAMP, &ignorstamp);
    cm_ssl_setopt(ssl_id, CM_SSL_PARAM_IGNOREVERIFY, &ignorverify);
    cm_ssl_setopt(ssl_id, CM_SSL_PARAM_VERIFY, &verify);

    /* 开始握手连接，握手阶段不可进行数据读取，使用标志位控制。cm_ssl_conn是同步接口，直接返回握手结果 */
    int ret = cm_ssl_conn((void**)&ctx->ssl_ctx, ssl_id, ctx->sock, 0);
    if (ret == 0)
    {
        ctx->connect_status = 3;

        socket_channel_send_register_content(ctx);
    }

    console_log("[tcp channel %d] ssl connect end, ret is %d", ctx->id, ret);
}

/**
 * @brief 发送注册包
 */
static void socket_channel_send_register_content(tcp_channel_ctx_t* ctx)
{
    console_log("[tcp channel %d] send register content start, type=%s", ctx->id, ctx->param->register_type);

    if (not_empty(ctx->param->register_type) && not_empty(ctx->param->register_content))
    {

        uint8_t* send_data = NULL;
        size_t len = 0;
        if (strcmp(ctx->param->register_type, "hex") == 0)
        {
            size_t send_data_len = strlen(ctx->param->register_content);
            send_data = (uint8_t*)cm_malloc(send_data_len);
            if (send_data == NULL)
            {
                return;
            }
            memset(send_data, 0, send_data_len);
            len = hex_to_bytes((uint8_t*)ctx->param->register_content, send_data, send_data_len, 0);
        }
        else if (strcmp(ctx->param->register_type, "string") == 0)
        {
            send_data = (uint8_t*)render_template((char*)ctx->param->register_content);
            if (send_data == NULL)
            {
                return;
            }
            len = strlen((char*)send_data);
        }

        if (len > 0 && send_data != NULL)
        {
            console_log("[tcp channel %d] send register content, len is %d", ctx->id, len);

            gc_data_t* gc_data = gc_data_init(send_data, 1);
            if (gc_data == NULL)
            {
                free_ptr(send_data);
                return;
            }

            tcp_channel_send(ctx, gc_data, len);
        }
    }

    console_log("[tcp channel %d] send register content end", ctx->id);
}

/**
 * @brief socket连接线程处理
 */
static void tcp_channel_conn_thread(void* param)
{
    tcp_channel_ctx_t* ctx = (tcp_channel_ctx_t*)param;

    console_log("[tcp channel %d] connect thread start", ctx->id);

    while (TRUE)
    {
        // 如果不是未连接状态，则跳过
        if (ctx->connect_status > 0)
        {
            osDelay(get_ms(5000));
            continue;
        }

        /* 创建socket */
        int sock = -1;
        sock = cm_asocket_open(AF_INET, SOCK_STREAM, IPPROTO_TCP, tcp_channel_asocket_event, ctx);
        if (sock == -1)
        {
            console_log("[tcp channel %d] connect thread, asocket open failed", ctx->id);
            continue;;
        }
        ctx->sock = sock;

        console_log("[tcp channel %d] connect thread, try to connect", ctx->id);

        // 设置状态为连接中
        ctx->connect_status = 1;

        // 连接socket
        struct sockaddr_in server_addr;
        memset(&server_addr, 0, sizeof(server_addr));
        server_addr.sin_family = AF_INET;
        server_addr.sin_addr.s_addr = inet_addr(ctx->param->host);
        server_addr.sin_port = htons(ctx->param->port);
        int ret = cm_asocket_connect(sock, (const struct sockaddr*)&server_addr, sizeof(server_addr));
        if (ret == -1)
        {
            ctx->connect_status = 0;
        }

        console_log("[tcp channel %d] connect thread, connect ret is %d", ctx->id, ret);

        osDelay(get_ms(3000));
    }
}

/**
 * @brief 读取线程
 */
static void socket_channel_read_thread(void* param)
{
    tcp_channel_ctx_t* ctx = (tcp_channel_ctx_t*)param;

    console_log("[tcp channel %d] read thread start", ctx->id);

    while (TRUE)
    {
        // 等待释放信号
        osSemaphoreAcquire(ctx->read_thread_sem, osWaitForever);

        console_log("[tcp channel %d] read thread, read start", ctx->id);

        // 获取缓存区
        char* current_buffer = ctx->buff;
        uint32_t current_len = ctx->buff_len;

        // 缓冲区可读取长度
        int data_len = 0;

        // 获取缓冲区可读取长度
        // SSL
        if (ctx->param->ssl_enable)
        {
            cm_ssl_read(ctx->ssl_ctx, NULL, 0);
            data_len = cm_ssl_get_bytes_avail(ctx->ssl_ctx);
        }
        // 普通socket
        else
        {
            cm_asocket_ioctl(ctx->sock, FIONREAD, &data_len);
        }

        console_log("[tcp channel %d] read thread, read data len is %d", ctx->id, data_len);

        // 尝试重新分配内存
        char* new_buffer = cm_realloc(current_buffer, current_len + data_len + 1);
        if (new_buffer == NULL)
        {
            console_log("[tcp channel %d] read thread, realloc failed", ctx->id);
            free_ptr(current_buffer);
            ctx->buff = NULL;
            ctx->buff_len = 0;
            continue;
        }
        current_buffer = new_buffer;

        // 读取数据
        // SSL
        if (ctx->param->ssl_enable)
        {
            data_len = cm_ssl_read(ctx->ssl_ctx, current_buffer + current_len, data_len);
        }
        // 普通socket
        else
        {
            data_len = cm_asocket_recv(ctx->sock, current_buffer + current_len, data_len, 0);
        }

        // 保存数据
        ctx->buff = current_buffer;
        ctx->buff_len = current_len + data_len;

        // 启动定时器
        osTimerStart(ctx->pack_timer_id, get_ms(100));

        console_log("[tcp channel %d] read thread, read end, current_len is %d", ctx->id, current_len);
    }
}

/**
 * @brief 分包定时器处理函数
 */
static void tcp_channel_pack_timer(void* param)
{
    tcp_channel_ctx_t* ctx = (tcp_channel_ctx_t*)param;

    console_log("[tcp channel %d] get pack, len=%d", ctx->id, ctx->buff_len);

    comm_msg_t* msg = (comm_msg_t*)cm_malloc(sizeof(comm_msg_t));
    if (msg == NULL)
    {
        console_log("[tcp channel %d] get pack, malloc failed", ctx->id);
        free_ptr(ctx->buff);
        ctx->buff = NULL;
        ctx->buff_len = 0;
        return;
    }

    msg->data = (uint8_t*)ctx->buff;
    msg->len = ctx->buff_len;

    osMessageQueuePut(ctx->consume_queue_id, &msg, 0, 0);
    ctx->buff = NULL;
    ctx->buff_len = 0;
}

/**
 * @brief 消费线程处理函数
 */
static void socket_channel_consume_thread(void* param)
{
    tcp_channel_ctx_t* ctx = (tcp_channel_ctx_t*)param;

    console_log("[tcp channel %d] consume thread start", ctx->id);

    while (TRUE)
    {
        comm_msg_t* msg = NULL;
        if (osMessageQueueGet(ctx->consume_queue_id, &msg, 0, osWaitForever) == osOK)
        {
            console_log("[tcp channel %d] consume thread, consume start", ctx->id);
            ctx->callback((void*)ctx, msg->data, msg->len);
            console_log("[tcp channel %d] consume thread, consume end", ctx->id);
            free_ptr(msg);
        }
    }
}

/**
 * @brief 心跳定时器处理函数
 */
static void tcp_channel_heart_thread(void* param)
{
    tcp_channel_ctx_t* ctx = (tcp_channel_ctx_t*)param;

    console_log("[tcp channel %d] heart thread start", ctx->id);

    while (TRUE)
    {
        if (ctx->connect_status != 3)
        {
            osDelay(get_ms(3000));
            continue;
        }

        console_log("[tcp channel %d] heart send start", ctx->id);

        uint8_t* template = (uint8_t*)ctx->param->heart_content;
        uint8_t* send_data;
        size_t len;
        // 处理HEX字符串
        if (strcmp(ctx->param->heart_type, "hex") == 0)
        {
            len = strlen((const char*)template);
            send_data = (uint8_t*)cm_malloc(len);
            if (send_data == NULL)
            {
                console_log("[tcp channel %d] heart send failed, malloc failed", ctx->id);
                continue;
            }

            len = hex_to_bytes(template, send_data, len, 0);
            if (len == 0)
            {
                free_ptr(send_data);
                console_log("[tcp channel %d] heart send failed, hex to bytes no data", ctx->id);
                continue;
            }
        }
        // 处理字符串模板
        else
        {
            send_data = (uint8_t*)render_template((char*)template);
            if (send_data == NULL)
            {
                console_log("[tcp channel %d] heart send failed, render template failed", ctx->id);
                continue;
            }
            len = strlen((char*)send_data);
        }

        // 创建gc_data
        gc_data_t* gc_data = gc_data_init(send_data, 1);
        if (gc_data == NULL)
        {
            free_ptr(send_data);
            console_log("[tcp channel %d] heart send failed, gc data init failed", ctx->id);
            continue;
        }

        tcp_channel_send(ctx, gc_data, len);

        console_log("tcp channel %d heart send end", ctx->id);

        osDelay(get_ms(ctx->param->heart_interval));
    }

}

/**
 * @brief 发送线程处理函数
 */
static void socket_channel_send_thread(void* param)
{
    tcp_channel_ctx_t* ctx = (tcp_channel_ctx_t*)param;

    console_log("[tcp channel %d] send thread start", ctx->id);

    while (TRUE)
    {
        comm_gc_msg_t* msg = NULL;
        if (osMessageQueueGet(ctx->send_queue_id, &msg, 0, osWaitForever) == osOK)
        {
            size_t len = msg->len;
            gc_data_t* gc_data = msg->data;
            uint8_t* data = (uint8_t*)gc_data->data;

            // 释放msg
            free_ptr(msg);

            console_log("[tcp channel %d] send thread, send start", ctx->id);

            // SSL
            if (ctx->param->ssl_enable)
            {
                int send_len = 0;
                while (send_len < len)
                {
                    int ret = cm_ssl_write(ctx->ssl_ctx, (void*)(data + send_len), len - send_len);

                    // MBEDTLS正在等待发送或等待接收
                    if (ret == MBEDTLS_ERR_SSL_WANT_WRITE || ret == MBEDTLS_ERR_SSL_WANT_READ)
                    {
                        continue;
                    }
                    // 发送成功
                    else if (ret >= 0)
                    {
                        send_len += ret;
                    }
                    //发送异常
                    else
                    {
                        send_len = ret;
                        break;
                    }
                }
            }
            // 普通socket
            else
            {
                cm_asocket_send(ctx->sock, data, len, 0);
            }

            // 释放内存
            gc_try_free(gc_data);

            console_log("[tcp channel %d] send thread, send end", ctx->id);
        }
    }
}

/**
 * @brief tcp通道发送
 *
 * @param ctx tcp通道上下文
 * @param data 数据
 * @param len 数据长度
 */
COMMON_RES_T tcp_channel_send(tcp_channel_ctx_t* ctx, gc_data_t* data, size_t len)
{
    console_log("[tcp channel %d] send start", ctx->id);

    comm_gc_msg_t* msg = (comm_gc_msg_t*)cm_malloc(sizeof(comm_gc_msg_t));
    if (msg == NULL)
    {
        console_log("[tcp channel %d] send failed, malloc msg failed", ctx->id);
        return RES_ERROR;
    }

    msg->data = data;
    msg->len = len;

    osMessageQueuePut(ctx->send_queue_id, &msg, 0, 0);

    console_log("[tcp channel %d] send end", ctx->id);

    return RES_SUCCESS;
}

/**
 * @brief 关闭socket
 */
static void tcp_channel_close_handle(cm_eloop_event_handle_t event, void* cb_param)
{
    /* 注销Event */
    cm_eloop_unregister_event(event);

    tcp_channel_ctx_t* ctx = (tcp_channel_ctx_t*)cb_param;

    // 停止心跳线程
    if (ctx->heart_thread_id != NULL)
    {
        osThreadTerminate(ctx->heart_thread_id);
        ctx->heart_thread_id = NULL;
    }

    // 关闭连接线程
    osThreadTerminate(ctx->connect_thread_id);
    ctx->connect_thread_id = NULL;

    // 关闭socket连接
    cm_asocket_close(ctx->sock);
    ctx->sock = -1;

    // 停止发送线程
    osThreadTerminate(ctx->send_thread_id);
    ctx->send_thread_id = NULL;

    // 停止读取线程
    osThreadTerminate(ctx->read_thread_id);
    ctx->read_thread_id = NULL;

    // 停止读消费程
    osThreadTerminate(ctx->consume_thread_id);
    ctx->consume_thread_id = NULL;

    // 删除消息队列
    osMessageQueueDelete(ctx->send_queue_id);
    ctx->send_queue_id = NULL;
    osMessageQueueDelete(ctx->consume_queue_id);
    ctx->consume_queue_id = NULL;

    // 删除信号量
    osSemaphoreDelete(ctx->read_thread_sem);
    ctx->read_thread_sem = NULL;

    // 删除打包定时器
    osTimerStop(ctx->pack_timer_id);
    osTimerDelete(ctx->pack_timer_id);
    ctx->pack_timer_id = NULL;

    // 释放内存
    if (ctx->buff_len > 0)
    {
        free_ptr(ctx->buff);
        ctx->buff = NULL;
        ctx->buff_len = 0;
    }

    ctx->callback = NULL;

    free_ptr(ctx);
}

/**
 * @brief tcp通道关闭
 *
 * @param ctx tcp通道上下文
 */
void tcp_channel_close(tcp_channel_ctx_t* ctx)
{
    if (ctx->param->ssl_enable && ctx->ssl_ctx != NULL)
    {
        cm_ssl_close((void**)ctx->ssl_ctx);
    }

    /* 注册Event(将命令封装成Event发送到eloop执行) */
    cm_eloop_event_handle_t cmd_CLOSE_recv_event = cm_eloop_register_event(cm_asocket_eloop(), tcp_channel_close_handle, ctx);
    /* 发送到eloop执行 */
    cm_eloop_post_event(cmd_CLOSE_recv_event);
}