#include "common.h"
#include "my_util.h"
#include "cm_mem.h"
#include "stdarg.h"
#include "stdio.h"
#include "stdlib.h"
#include "string.h"
#include "cm_uart.h"
#include "cm_os.h"


static osThreadId_t gc_task_id = NULL;
static osMessageQueueId_t gc_queue_id = NULL;

void gc_task(void* param)
{
    while (TRUE)
    {
        gc_data_t* msg = NULL;
        if (osMessageQueueGet(gc_queue_id, &msg, NULL, osWaitForever) == osOK)
        {
            if (msg->ref == 0)
            {
                cm_free(msg->data);
                cm_free(msg);
                msg = NULL;
            }
        }
    }
}

/**
 * @brief 创建GC任务
 */
void gc_task_init()
{
    if (gc_task_id != NULL)
        return;

    gc_queue_id = osMessageQueueNew(10, sizeof(gc_data_t*), NULL);

    osThreadAttr_t attr = {
        .name = "gc_task",
        .stack_size = 1024,
        .priority = osPriorityNormal,
    };
    gc_task_id = osThreadNew(gc_task, NULL, &attr);
}

/**
 * @brief 尝试释放数据
 * @param msg 消息指针
 */
void gc_try_free(gc_data_t* msg)
{
    if (msg == NULL || gc_queue_id == NULL)
        return;

    if (msg->ref > 0)
        msg->ref--;

    osMessageQueuePut(gc_queue_id, &msg, 0, 0);
}

/**
 * @brief 创建动态列表
 * @param size 列表大小
 * @return 列表指针
 */
dynamic_list_t* dynamic_list_init(uint32_t element_size)
{
    dynamic_list_t* list = (dynamic_list_t*)cm_malloc(sizeof(dynamic_list_t));
    if (list == NULL)
    {
        console_log("dynamic_list_init failed");
        return NULL;
    }

    list->data = NULL;
    list->size = 0;
    list->element_size = element_size;
    return list;
}

/**
 * @brief 创建GC数据
 * @param data 数据指针
 * @param ref 引用计数
 * @return GC数据指针
 */
gc_data_t* gc_data_init(void* data, uint32_t ref)
{
    gc_data_t* msg = (gc_data_t*)cm_malloc(sizeof(gc_data_t));
    if (msg == NULL)
        return NULL;

    msg->data = data;
    msg->ref = ref;

    return msg;
}

/**
 * @brief 释放动态列表
 * @param list 列表指针
 */
void dynamic_list_free(dynamic_list_t* list)
{
    if (list == NULL)
        return;

    if (list->data != NULL)
    {
        cm_free(list->data);
        cm_free(list);
    }
}

/**
 * @brief 添加数据到动态列表
 * @param list 列表指针
 * @param data 数据指针
 */
void dynamic_list_add(dynamic_list_t* list, void* data)
{
    if (list == NULL)
        return;

    void** new_ptr = cm_realloc(list->data, list->element_size * (list->size + 1));
    if (new_ptr == NULL)
        return;

    list->data = new_ptr;
    list->data[list->size] = data;
    list->size++;
}

/**
 * @brief 删除数据从动态列表
 * @param list 列表指针
 * @param data 数据指针
 */
void dynamic_list_remove(dynamic_list_t* list, void* data)
{
    if (list == NULL)
        return;

    // 找到元素位置
    int i = dynamic_list_find(list, data);

    // 删除元素
    if (i > -1)
    {
        if (list->size <= 1)
        {
            list->size = 0;
            free(list->data);
            list->data = NULL;
        }
        else
        {
            void** new_ptr = cm_malloc(list->element_size * (list->size - 1));
            if (new_ptr == NULL)
                return;

            for (int j = 0; j < i; j++)
                new_ptr[j] = list->data[j];

            for (int j = i + 1; j < list->size; j++)
                new_ptr[j - 1] = list->data[j];

            free(list->data);
            list->data = new_ptr;
            list->size--;
        }
    }
}

/**
 * @brief 查找数据在动态列表中的索引
 * @param list 列表指针
 * @param data 数据指针
 * @return 数据索引，未找到返回-1
 */
int dynamic_list_find(dynamic_list_t* list, void* data)
{
    if (list == NULL)
        return -1;

    int i = -1;
    for (i = 0; i < list->size; i++)
    {
        if (list->data[i] == data)
            break;
    }

    return i;
}

/**
 * @brief 根据索引获取数据
 * @param list 列表指针
 * @param index 索引
 * @return 数据指针
 */
void* dynamic_list_get(dynamic_list_t* list, uint32_t index)
{
    if (list == NULL)
        return NULL;

    if (index >= list->size)
        return NULL;

    return list->data[index];
}

static uint8_t global_id = 1;

/**
 * @brief 获取下一个ID
 * @return ID
 */
uint8_t next_global_id(void)
{
    return global_id++;
}

/**
 * @brief 重置ID
 * @param id ID
 */
void reset_global_id(uint8_t id)
{
    global_id = id;
}

/**
 * @brief 拷贝字符串
 * @param param 源字符串指针
 * @return 拷贝后的字符串指针
 */
char* copy_str(char* param)
{
    return copy_str_by_len(param, strlen(param));
}

/**
 * @brief 拷贝指定长度的字符串
 * @param param 源字符串指针
 * @param len 拷贝长度
 * @return 拷贝后的字符串指针
 */
char* copy_str_by_len(char* param, size_t len)
{
    char* copy = NULL;
    if (param != NULL && len > 0)
    {
        copy = cm_malloc(len + 1);
        if (copy == NULL)
            return NULL;
        memcpy(copy, param, len);
        copy[len] = '\0';
    }
    return copy;
}

/**
 * @brief AT串口打印
 */
void uart_printf(uint8_t dev_num, char* str, ...)
{
    char s[600] = { 0 };
    va_list args;
    int len;

    if ((str == NULL) || (strlen(str) == 0))
    {
        return;
    }

    va_start(args, str);
    len = vsnprintf((char*)s, 600, str, args);
    va_end(args);
    cm_uart_write(dev_num, s, len, 1000);
}

/**
 * @brief 获取数据的CRC校验值
 */
uint16_t modbus_crc16(uint8_t* data, uint16_t size)
{
    uint16_t crc = 0xFFFF;
    for (uint16_t i = 0; i < size; ++i)
    {
        crc ^= (*data++);
        for (uint8_t b = 0; b < 8; ++b)
        {
            if (crc & 0x1)
                crc = (crc >> 1) ^ 0xA001;
            else
                crc >>= 1;
        }
    }
    return crc;
}

/**
 * @brief 十六进制字符串转字节数组
 * @param hex_str 十六进制字符串
 * @param buff 缓冲区
 * @param buff_len 缓冲区大小
 * @param crc 是否添加CRC校验
 * @return 字节数
 */
size_t hex_to_bytes(uint8_t* hex_str, uint8_t* buff, size_t buff_len, uint8_t crc)
{
    if (hex_str == NULL || buff == NULL || buff_len == 0)
        return 0;

    size_t len = strlen((const char*)hex_str);

    // 校验需要的字节数是否大于缓冲区
    size_t need_len = (len % 2 == 0 ? len / 2 : len / 2 + 1) + (crc ? 1 : 0);
    if (need_len > buff_len)
        return 0;

    uint32_t i;
    uint32_t byte_index = 0;
    uint32_t effect_count = 0;
    uint8_t is_high_4 = 1;
    uint8_t hex_val = 0;
    uint8_t is_valid = 0;
    for (i = 0; i < len; i++)
    {
        uint8_t hex_char = hex_str[i];

        if (hex_char >= '0' && hex_char <= '9')
        {
            hex_val = hex_char - '0';
            is_valid = 1;
        }
        else if (hex_char >= 'a' && hex_char <= 'f')
        {
            hex_val = hex_char - 'a' + 10;
            is_valid = 1;
        }
        else if (hex_char >= 'A' && hex_char <= 'F')
        {
            hex_val = hex_char - 'A' + 10;
            is_valid = 1;
        }

        // 如果不是hex字符，则跳过
        if (!is_valid)
            continue;

        // 赋值
        buff[byte_index] |= hex_val << (is_high_4 ? 4 : 0);

        // 增加有效字节数
        if (is_high_4)
            effect_count++;
        else
        {
            byte_index++;
            buff[byte_index] = 0x00;
        }

        // 翻转高4位和低4位
        is_high_4 = !is_high_4;
    }

    if (effect_count > 0 && crc)
    {
        uint16_t crc_val = modbus_crc16(buff, effect_count);

        buff[effect_count] = crc_val & 0xFF;
        buff[effect_count + 1] = (crc_val >> 8) & 0xFF;

        effect_count += 2;
    }

    return effect_count;
}

const char hex_chars[] = "0123456789ABCDEF";

/**
 * @brief 字节数组转十六进制字符串
 * @param bytes 字节数组
 * @param len 字节数
 * @return 十六进制字符串
 */
char* bytes_to_hex(uint8_t* bytes, size_t len)
{
    size_t hex_len = len * 2 + 1;
    char* hex_str = (char*)cm_malloc(hex_len);
    if (hex_str == NULL)
    {
        console_log("failed to malloc");
        return NULL;
    }
    memset(hex_str, 0, hex_len);

    for (size_t i = 0; i < len; i++)
    {
        hex_str[i * 2] = hex_chars[(bytes[i] >> 4) & 0x0F];
        hex_str[i * 2 + 1] = hex_chars[bytes[i] & 0x0F];
    }

    return hex_str;
}

/**
 * @brief 字符串替换
 *
 * @param str 原字符串
 * @param from 需要替换的子串
 * @param to 目标子串
 * @return 替换后的字符串
 */
char* str_replace(const char* str, const char* from, const char* to)
{
    // 首先计算目标子串的长度
    size_t from_len = strlen(from);
    // 如果目标子串为空，则直接复制原字符串
    if (from_len == 0)
        return copy_str((char*)str);

    // 计算新子串的长度
    size_t to_len = strlen(to);

    // 初始化变量
    char* result;
    size_t result_length = 0;
    size_t str_len = strlen(str);
    int count = 0;

    // 第一次遍历：确定结果字符串需要的空间大小
    for (size_t pos = 0; pos < str_len; pos++)
    {
        if (strstr(&str[pos], from) == &str[pos])
        {
            count++;
            pos += from_len - 1;
        }
    }

    // 计算结果字符串所需的总长度
    result_length = str_len + count * (to_len - from_len);
    result = (char*)cm_malloc(result_length + 1);

    // 第二次遍历：构造结果字符串
    size_t current_pos_in_result = 0;
    for (size_t pos = 0; pos < str_len; )
    {
        if (strstr(&str[pos], from) == &str[pos])
        {
            strcat(&result[current_pos_in_result], to);
            current_pos_in_result += to_len;
            pos += from_len;
        }
        else
        {
            result[current_pos_in_result++] = str[pos++];
        }
    }
    result[result_length] = '\0';

    return result;
}