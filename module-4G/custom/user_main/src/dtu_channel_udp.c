#include "common.h"
#include "dtu_channel.h"
#include "my_util.h"
#include "dtu_channel_udp.h"
#include "cm_asocket.h"
#include "lwip/lwip/netdb.h"
#include "lwip/lwip/sockets.h"

// 函数向前声明
static void udp_channel_send_register_content(udp_channel_ctx_t* ctx);
static void udp_channel_read_thread(void* param);
static void udp_channel_pack_timer(void* param);
static void udp_channel_heart_thread(void* param);
static void udp_channel_consume_thread(void* param);
static void udp_channel_send_thread(void* param);

/**
 * @brief udp通道初始化
 *
 * @param param socket参数
 * @param id 通道id
 * @param callback 接收回调
 * @return udp_channel_ctx_t* udp通道上下文
 */
udp_channel_ctx_t* udp_channel_init(dtu_socket_param_t* param, uint8_t id, data_receive_callback_t callback)
{
    console_log("[udp channel %d] init start", id);

    udp_channel_ctx_t* ctx = (udp_channel_ctx_t*)cm_malloc(sizeof(udp_channel_ctx_t));
    if (ctx == NULL)
    {
        console_log("[udp channel %d] init failed, malloc ctx failed", id);
        return NULL;
    }

    ctx->param = param;
    ctx->id = id;
    ctx->connect_status = 0;
    ctx->sock = -1;
    ctx->callback = callback;

    // 开启socket读线程
    osThreadAttr_t read_thread_attr = { 0 };
    char read_thread_name[30];
    sprintf(read_thread_name, "udp channel %d read", ctx->id);
    read_thread_attr.name = read_thread_name;
    read_thread_attr.stack_size = 1024 * 4;
    read_thread_attr.priority = osPriorityNormal;
    ctx->read_thread_id = osThreadNew((osThreadFunc_t)udp_channel_read_thread, ctx, &read_thread_attr);

    // 创建socket分包定时器
    ctx->pack_timer_id = osTimerNew((osTimerFunc_t)udp_channel_pack_timer, osTimerOnce, ctx, NULL);

    // 创建发送队列
    ctx->send_queue_id = osMessageQueueNew(10, sizeof(comm_gc_msg_t*), NULL);

    // 创建发送线程
    osThreadAttr_t send_thread_attr = { 0 };
    char send_thread_name[30];
    sprintf(send_thread_name, "udp channel %d send", ctx->id);
    send_thread_attr.name = send_thread_name;
    send_thread_attr.stack_size = 1024;
    send_thread_attr.priority = osPriorityNormal;
    ctx->read_thread_id = osThreadNew((osThreadFunc_t)udp_channel_send_thread, ctx, &send_thread_attr);

    // 创建消费队列
    ctx->consume_queue_id = osMessageQueueNew(10, sizeof(comm_msg_t*), NULL);

    // 创建消费线程
    osThreadAttr_t consume_thread_attr = { 0 };
    char consume_thread_name[30];
    sprintf(consume_thread_name, "udp channel %d consume", ctx->id);
    consume_thread_attr.name = consume_thread_name;
    consume_thread_attr.stack_size = 2048;
    consume_thread_attr.priority = osPriorityNormal;
    ctx->consume_thread_id = osThreadNew((osThreadFunc_t)udp_channel_consume_thread, ctx, &consume_thread_attr);

    // 开启socket心跳线程
    if (ctx->param->heart_interval > 0 && not_empty(ctx->param->heart_content))
    {
        osThreadAttr_t heart_thread_attr = { 0 };
        char heart_thread_name[30];
        sprintf(heart_thread_name, "udp channel %d heart", ctx->id);
        heart_thread_attr.name = heart_thread_name;
        heart_thread_attr.stack_size = 2048;
        heart_thread_attr.priority = osPriorityNormal;
        ctx->read_thread_id = osThreadNew((osThreadFunc_t)udp_channel_heart_thread, ctx, &heart_thread_attr);
    }

    console_log("[udp channel %d] init end", id);

    return ctx;
}

/**
 * @brief 发送注册包
 */
static void udp_channel_send_register_content(udp_channel_ctx_t* ctx)
{
    console_log("[udp channel %d] send register content start, type=%s", ctx->id, ctx->param->register_type);

    if (not_empty(ctx->param->register_type) && not_empty(ctx->param->register_content))
    {

        uint8_t* send_data = NULL;
        size_t len = 0;
        if (strcmp(ctx->param->register_type, "hex") == 0)
        {
            size_t send_data_len = strlen(ctx->param->register_content);
            send_data = (uint8_t*)cm_malloc(send_data_len);
            if (send_data == NULL)
            {
                return;
            }
            memset(send_data, 0, send_data_len);
            len = hex_to_bytes((uint8_t*)ctx->param->register_content, send_data, send_data_len, 0);
        }
        else if (strcmp(ctx->param->register_type, "string") == 0)
        {
            send_data = (uint8_t*)render_template((char*)ctx->param->register_content);
            if (send_data == NULL)
            {
                return;
            }
            len = strlen((char*)send_data);
        }

        if (len > 0 && send_data != NULL)
        {
            console_log("[udp channel %d] send register content, len is %d", ctx->id, len);

            gc_data_t* gc_data = gc_data_init(send_data, 1);
            if (gc_data == NULL)
            {
                free_ptr(send_data);
                return;
            }

            udp_channel_send(ctx, gc_data, len);
        }
    }

    console_log("[udp channel %d] send register content end", ctx->id);
}

/**
 * @brief 读取线程
 */
static void udp_channel_read_thread(void* param)
{
    udp_channel_ctx_t* ctx = (udp_channel_ctx_t*)param;

    console_log("[udp channel %d] read thread start", ctx->id);

    int buff_size = 128;
    char* buff[buff_size];
    int read_len = 0;
    int ret = 0;
    fd_set sets;
    struct timeval timeout;

    while (TRUE)
    {
        // 如果socket为空，则创建socket
        if (ctx->sock == -1)
        {
            /* 创建socket */
            int sock = -1;
            sock = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
            if (sock == -1)
            {
                console_log("[udp channel %d] read failed, socket create failed", ctx->id);
                continue;
            }
            ctx->sock = sock;

            ctx->connect_status = 3;

            // 发送注册包
            udp_channel_send_register_content(ctx);

            osDelay(get_ms(1000));
        }

        // 设置select
        FD_ZERO(&sets);
        FD_SET(ctx->sock, &sets);

        // 设置select超时时间
        timeout.tv_sec = 10;
        timeout.tv_usec = 0;

        ret = select(ctx->sock + 1, &sets, NULL, NULL, &timeout);

        if (ret > 0)
        {
            read_len = recv(ctx->sock, buff, buff_size, 0);
            if (read_len > 0)
            {
                console_log("[udp channel %d] read thread, read %d bytes", ctx->id, read_len);

                // 获取缓存区
                char* current_buffer = ctx->buff;
                uint32_t current_len = ctx->buff_len;

                // 尝试重新分配内存
                char* new_buffer = cm_realloc(current_buffer, current_len + read_len + 1);
                if (new_buffer == NULL)
                {
                    console_log("[udp channel %d] read thread, realloc failed", ctx->id);
                    free_ptr(current_buffer);
                    ctx->buff = NULL;
                    ctx->buff_len = 0;
                    memset(buff, 0, buff_size);
                    continue;
                }
                current_buffer = new_buffer;

                // 拷贝数据
                memcpy(current_buffer + current_len, buff, read_len);
                current_len += read_len;
                current_buffer[current_len] = '\0';

                // 保存数据
                ctx->buff = current_buffer;
                ctx->buff_len = current_len;

                // 启动定时器
                osTimerStart(ctx->pack_timer_id, get_ms(100));

                console_log("[udp channel %d] read thread, read end, current_len is %d", ctx->id, current_len);
            }

            // 清空缓存区
            memset(buff, 0, buff_size);
        }
        /* 走到这是select等待超时 */
        else if (0 == ret)
        {
            console_log("[udp channel %d] read thread, select timeout", ctx->id);
        }
        /* 走到这是当前socket已关闭 */
        else
        {
            close(ctx->sock);
            ctx->sock = -1;
            ctx->connect_status = 0;
            free_ptr(ctx->buff);
            ctx->buff = NULL;
            ctx->buff_len = 0;
        }
    }
}

/**
 * @brief 分包定时器处理函数
 */
static void udp_channel_pack_timer(void* param)
{
    udp_channel_ctx_t* ctx = (udp_channel_ctx_t*)param;

    console_log("[udp channel %d] get pack, len=%d", ctx->id, ctx->buff_len);

    comm_msg_t* msg = (comm_msg_t*)cm_malloc(sizeof(comm_msg_t));
    if (msg == NULL)
    {
        console_log("[udp channel %d] get pack, malloc failed", ctx->id);
        free_ptr(ctx->buff);
        ctx->buff = NULL;
        ctx->buff_len = 0;
        return;
    }

    msg->data = (uint8_t*)ctx->buff;
    msg->len = ctx->buff_len;

    osMessageQueuePut(ctx->consume_queue_id, &msg, 0, 0);
    ctx->buff = NULL;
    ctx->buff_len = 0;
}

/**
 * @brief 消费线程处理函数
 */
static void udp_channel_consume_thread(void* param)
{
    udp_channel_ctx_t* ctx = (udp_channel_ctx_t*)param;

    console_log("[udp channel %d] consume thread start", ctx->id);

    while (TRUE)
    {
        comm_msg_t* msg = NULL;
        if (osMessageQueueGet(ctx->consume_queue_id, &msg, 0, osWaitForever) == osOK)
        {
            console_log("[udp channel %d] consume thread, consume start", ctx->id);
            ctx->callback((void*)ctx, msg->data, msg->len);
            console_log("[udp channel %d] consume thread, consume end", ctx->id);
            free_ptr(msg);
        }
    }
}

/**
 * @brief 心跳定时器处理函数
 */
static void udp_channel_heart_thread(void* param)
{
    udp_channel_ctx_t* ctx = (udp_channel_ctx_t*)param;

    console_log("[udp channel %d] heart thread start", ctx->id);

    while (TRUE)
    {
        if (ctx->connect_status != 3)
        {
            osDelay(get_ms(3000));
            continue;
        }

        console_log("[udp channel %d] heart send start", ctx->id);

        uint8_t* template = (uint8_t*)ctx->param->heart_content;
        uint8_t* send_data;
        size_t len;
        // 处理HEX字符串
        if (strcmp(ctx->param->heart_type, "hex") == 0)
        {
            len = strlen((const char*)template);
            send_data = (uint8_t*)cm_malloc(len);
            if (send_data == NULL)
            {
                console_log("[udp channel %d] heart send failed, malloc failed", ctx->id);
                continue;
            }

            len = hex_to_bytes(template, send_data, len, 0);
            if (len == 0)
            {
                free_ptr(send_data);
                console_log("[udp channel %d] heart send failed, hex to bytes no data", ctx->id);
                continue;
            }
        }
        // 处理字符串模板
        else
        {
            send_data = (uint8_t*)render_template((char*)template);
            if (send_data == NULL)
            {
                console_log("[udp channel %d] heart send failed, render template failed", ctx->id);
                continue;
            }
            len = strlen((char*)send_data);
        }

        // 创建gc_data
        gc_data_t* gc_data = gc_data_init(send_data, 1);
        if (gc_data == NULL)
        {
            free_ptr(send_data);
            console_log("[udp channel %d] heart send failed, gc data init failed", ctx->id);
            continue;
        }

        udp_channel_send(ctx, gc_data, len);

        console_log("udp channel %d heart send end", ctx->id);

        osDelay(get_ms(ctx->param->heart_interval));
    }

}

/**
 * @brief 发送线程处理函数
 */
static void udp_channel_send_thread(void* param)
{
    udp_channel_ctx_t* ctx = (udp_channel_ctx_t*)param;

    console_log("[udp channel %d] send thread start", ctx->id);

    while (TRUE)
    {
        comm_gc_msg_t* msg = NULL;
        if (osMessageQueueGet(ctx->send_queue_id, &msg, 0, osWaitForever) == osOK)
        {
            size_t len = msg->len;
            gc_data_t* gc_data = msg->data;
            uint8_t* data = (uint8_t*)gc_data->data;

            // 释放msg
            free_ptr(msg);

            console_log("[udp channel %d] send thread, send start", ctx->id);

            struct sockaddr_in server_addr;
            memset(&server_addr, 0, sizeof(server_addr));
            server_addr.sin_len = sizeof(server_addr);
            server_addr.sin_family = AF_INET;
            server_addr.sin_port = htons(ctx->param->port);
            server_addr.sin_addr.s_addr = inet_addr(ctx->param->host);
            int ret = sendto(ctx->sock, (char*)data, len, 0, (const struct sockaddr*)&server_addr, sizeof(server_addr));

            console_log("[udp channel %d] send thread, send ret:%d", ctx->id, ret);

            // 释放内存
            gc_try_free(gc_data);

            console_log("[udp channel %d] send thread, send end", ctx->id);
        }
    }
}

/**
 * @brief udp通道发送
 *
 * @param ctx udp通道上下文
 * @param data 数据
 * @param len 数据长度
 */
COMMON_RES_T udp_channel_send(udp_channel_ctx_t* ctx, gc_data_t* data, size_t len)
{
    console_log("[udp channel %d] send start", ctx->id);

    comm_gc_msg_t* msg = (comm_gc_msg_t*)cm_malloc(sizeof(comm_gc_msg_t));
    if (msg == NULL)
    {
        console_log("[udp channel %d] send failed, malloc msg failed", ctx->id);
        return RES_ERROR;
    }

    msg->data = data;
    msg->len = len;

    osMessageQueuePut(ctx->send_queue_id, &msg, 0, 0);

    console_log("[udp channel %d] send end", ctx->id);

    return RES_SUCCESS;
}

/**
 * @brief 关闭socket
 */
static void udp_channel_close_handle(cm_eloop_event_handle_t event, void* cb_param)
{
    /* 注销Event */
    cm_eloop_unregister_event(event);

    udp_channel_ctx_t* ctx = (udp_channel_ctx_t*)cb_param;

    // 停止心跳线程
    if (ctx->heart_thread_id != NULL)
    {
        osThreadTerminate(ctx->heart_thread_id);
        ctx->heart_thread_id = NULL;
    }

    // 关闭socket连接
    close(ctx->sock);
    ctx->sock = -1;

    // 停止发送线程
    osThreadTerminate(ctx->send_thread_id);
    ctx->send_thread_id = NULL;

    // 停止读取线程
    osThreadTerminate(ctx->read_thread_id);
    ctx->read_thread_id = NULL;

    // 停止读消费程
    osThreadTerminate(ctx->consume_thread_id);
    ctx->consume_thread_id = NULL;

    // 删除消息队列
    osMessageQueueDelete(ctx->send_queue_id);
    ctx->send_queue_id = NULL;
    osMessageQueueDelete(ctx->consume_queue_id);
    ctx->consume_queue_id = NULL;

    // 删除打包定时器
    osTimerStop(ctx->pack_timer_id);
    osTimerDelete(ctx->pack_timer_id);
    ctx->pack_timer_id = NULL;

    // 释放内存
    if (ctx->buff_len > 0)
    {
        free_ptr(ctx->buff);
        ctx->buff = NULL;
        ctx->buff_len = 0;
    }

    ctx->callback = NULL;

    free_ptr(ctx);
}

/**
 * @brief udp通道关闭
 *
 * @param ctx udp通道上下文
 */
void udp_channel_close(udp_channel_ctx_t* ctx)
{
    /* 注册Event(将命令封装成Event发送到eloop执行) */
    cm_eloop_event_handle_t cmd_CLOSE_recv_event = cm_eloop_register_event(cm_asocket_eloop(), udp_channel_close_handle, ctx);
    /* 发送到eloop执行 */
    cm_eloop_post_event(cmd_CLOSE_recv_event);
}