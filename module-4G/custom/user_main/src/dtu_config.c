#include "config_uart.h"
#include "my_util.h"
#include "cm_uart.h"
#include "cm_sys.h"
#include "stdio.h"
#include "stdlib.h"
#include "stdarg.h"
#include "cm_os.h"
#include "cm_mem.h"
#include "cm_http.h"
#include "cm_ssl.h"
#include "cJSON.h"
#include "cm_fs.h"

// 函数向前声明
char* get_remote_config_content();
char* get_local_config_content();

/**
 * @brief 获取远程配置
 *
 * @param config 配置结构体指针
 * @return COMMON_RES_T 获取结果
 */
COMMON_RES_T get_dtu_config(dtu_config_t* config)
{
    COMMON_RES_T res = RES_ERROR;

    // 获取远程配置内容
    char* config_json_str = get_remote_config_content();
    if (config_json_str != NULL)
    {
        res = parse_dtu_config(config_json_str, config);
    }

    // 解析本地配置文件
    char* local_json_str = get_local_config_content();
    if (local_json_str != NULL && res != RES_SUCCESS)
    {
        res = parse_dtu_config(local_json_str, config);
    }

    // 如果都没解析成功，则返回错误
    if (RES_SUCCESS != res)
    {
        if (local_json_str != NULL) cm_free(local_json_str);
        if (config_json_str != NULL) cm_free(config_json_str);
        return res;
    }

    // 如果本地配置文件不存在，则保存
    if (!cm_fs_exist(LOCAL_CONFIG_FILE_NAME))
    {
        console_log("[dtu config] local config not exist, save");
        int32_t fd = cm_fs_open(LOCAL_CONFIG_FILE_NAME, CM_FS_WB);
        cm_fs_write(fd, config_json_str, strlen(config_json_str));
        cm_fs_close(fd);
    }
    // 如果本地配置文件存在，且内容不一致，则更新
    else if (local_json_str != NULL && config_json_str != NULL && strcmp(local_json_str, config_json_str))
    {
        console_log("[dtu config] local config not equal, update");
        cm_fs_delete(LOCAL_CONFIG_FILE_NAME);
        int32_t fd = cm_fs_open(LOCAL_CONFIG_FILE_NAME, CM_FS_WB);
        cm_fs_write(fd, config_json_str, strlen(config_json_str));
        cm_fs_close(fd);
    }

    // 释放资源
    if (local_json_str != NULL) cm_free(local_json_str);
    if (config_json_str != NULL) cm_free(config_json_str);

    return RES_SUCCESS;
}

/**
 * @brief 获取远程配置内容
 *
 * @return char* 配置内容
 */
char* get_remote_config_content()
{
    console_log("[dtu config] get remote config start");

    cm_httpclient_handle_t client = NULL;
    cm_httpclient_ret_code_e ret = CM_HTTP_RET_CODE_UNKNOWN_ERROR;
    ret = cm_httpclient_create((const u_int8_t*)CONFIG_SERVER, NULL, &client);
    if (CM_HTTP_RET_CODE_OK != ret || NULL == client)
    {
        console_log("[dtu config] get remote config create httpclient error");
        return NULL;
    }

    cm_httpclient_cfg_t client_cfg;
    client_cfg.ssl_enable = true;                                 // 使用SSL，即HTTPS连接方式。使用HTTP方式时该值为false
    client_cfg.ssl_id = 0;                                        // 设置SSL索引号
    client_cfg.cid = 0;                                           // 设置PDP索引号，目前不支持该项设置，设置任意值即可
    client_cfg.conn_timeout = HTTPCLIENT_CONNECT_TIMEOUT_DEFAULT; // 设置连接超时时间
    client_cfg.rsp_timeout = HTTPCLIENT_WAITRSP_TIMEOUT_DEFAULT;  // 设置响应超时时间
    client_cfg.dns_priority = 1;                                  // 设置DNS解析优先级，ipv6解析优先
    ret = cm_httpclient_set_cfg(client, client_cfg);              // 客户端参数设置

    if (CM_HTTP_RET_CODE_OK != ret || NULL == client)
    {
        console_log("[dtu config] get remote config httpclient cfg error");
        return NULL;
    }

    int verify = 0;
    char* ca_cert = CONFIG_SERVER_CERTIFICATE;
    cm_ssl_setopt(0, CM_SSL_PARAM_VERIFY, &verify);   // 设置SSL验证方式
    cm_ssl_setopt(0, CM_SSL_PARAM_CA_CERT, ca_cert);  // 设置CA证书

    cm_httpclient_sync_response_t response = {};
    cm_httpclient_sync_param_t param = { HTTPCLIENT_REQUEST_GET, (const uint8_t*)CONFIG_SERVER_CONFIG_URI, 0, NULL }; // GET方法，必须设置请求路径
    ret = cm_httpclient_sync_request(client, param, &response); // 发送请求，同步接口

    if (CM_HTTP_RET_CODE_OK != ret || NULL == client)
    {
        console_log("[dtu config] get remote config request error, ret=%d", ret);
        return NULL;
    }

    console_log("[dtu config] get remote config success");

    char* json_str = copy_str_by_len((char*)response.response_content, response.response_content_len);

    cm_httpclient_sync_free_data(client); // 释放响应数据
    cm_httpclient_terminate(client); // 终止HTTP客户端
    cm_httpclient_delete(client); // 删除HTTP客户端

    return json_str;
}

/**
 * @brief 获取本地配置信息
 *
 * @return char* 配置内容
 */
char* get_local_config_content()
{
    console_log("[dtu config] get local config start");
    char* local_json_str = NULL;

    if (cm_fs_exist(LOCAL_CONFIG_FILE_NAME))
    {
        // 打开文件
        int32_t fd = cm_fs_open(LOCAL_CONFIG_FILE_NAME, CM_FS_RB);
        if (fd < 0)
        {
            console_log("[dtu config] get local config open file error");
            return NULL;
        }

        // 获取文件大小
        int32_t file_size = cm_fs_filesize(LOCAL_CONFIG_FILE_NAME);
        if (file_size < 1)
        {
            console_log("[dtu config] get local config file size error");
            cm_fs_close(fd);
            return NULL;
        }

        // 申请内存
        local_json_str = cm_malloc(file_size + 1);
        if (local_json_str == NULL)
        {
            console_log("[dtu config] get local config malloc error");
            cm_fs_close(fd);
            return NULL;
        }

        // 读取文件内容
        cm_fs_read(fd, local_json_str, file_size);
        // 关闭文件
        cm_fs_close(fd);

        console_log("[dtu config] get local config success");

        // 确保字符串结束
        if (local_json_str != NULL)
        {
            local_json_str[file_size] = '\0';
        }
    }
    else
    {
        console_log("[dtu config] get local config file not exist");
    }

    return local_json_str;
}

/**
 * @brief 获取JSON布尔值
 *
 * @param json JSON对象
 * @param key 键
 * @param default_val 默认值
 * @return uint8_t 布尔值
 */
uint8_t get_json_bool_val(cJSON* json, char* key, u_int8_t default_val)
{
    cJSON* item = cJSON_GetObjectItem(json, key);
    if (item)
    {
        return item->type == cJSON_True ? 1 : 0;
    }
    return default_val;
}

/**
 * @brief 获取JSON整数值
 *
 * @param json JSON对象
 * @param key 键
 * @param default_val 默认值
 * @return uint32_t 整数值
 */
uint32_t get_json_int_val(cJSON* json, char* key, uint32_t default_val)
{
    cJSON* item = cJSON_GetObjectItem(json, key);
    if (item && item->type == cJSON_Number)
    {
        return item->valueint;
    }
    return default_val;
}

/**
 * @brief 获取JSON字符串
 *
 * @param json JSON对象
 * @param key 键
 * @param default_val 默认值
 * @return char* 字符串
 */
char* get_json_str_val(cJSON* json, char* key, char* default_val)
{
    cJSON* item = cJSON_GetObjectItem(json, key);
    if (item && item->type == cJSON_String)
    {
        return copy_str(item->valuestring);
    }
    return default_val;
}

/**
 * @brief 解析配置
 *
 * @param config_json_str 配置内容
 * @param config 配置结构体指针
 * @return COMMON_RES_T 解析结果
 */
COMMON_RES_T parse_dtu_config(char* config_json_str, dtu_config_t* config)
{
    console_log("[dtu config] parse config start");

    cJSON* root = cJSON_Parse(config_json_str);
    if (!root)
    {
        console_log("[dtu config] parse config error before: [%s]", cJSON_GetErrorPtr());
        return RES_ERROR;
    }

    config->version = get_json_str_val(root, "version", "unknow");
    config->dtu_code = get_json_str_val(root, "dtuCode", "unknow");

    // 串口参数
    cJSON* uart_config = cJSON_GetObjectItem(root, "collectConfig");
    if (uart_config && uart_config->type == cJSON_Array)
    {
        int len = cJSON_GetArraySize(uart_config);
        int uart_cnt = 0;
        for (int i = 0; i < len; i++)
        {
            cJSON* item = cJSON_GetArrayItem(uart_config, i);
            if (item && item->type == cJSON_Object)
            {
                // 申请内存
                dtu_uart_t* uart = (dtu_uart_t*)cm_malloc(sizeof(dtu_uart_t));
                if (uart == NULL)
                {
                    console_log("[dtu config] parse config uart malloc error");
                    return RES_ERROR;
                }
                memset(uart, 0, sizeof(dtu_uart_t));

                // 解析参数
                uart->dev_num = get_json_int_val(item, "collectChannelNum", 1);
                uart->enabled = get_json_bool_val(item, "enabled", 1);
                uart->baudrate = get_json_int_val(item, "baudRate", CM_UART_BAUDRATE_9600);
                uart->byte_size = get_json_int_val(item, "byteSize", CM_UART_BYTE_SIZE_8);
                uart->stop_bit = get_json_int_val(item, "stopBit", CM_UART_STOP_BIT_ONE);
                uart->parity = get_json_int_val(item, "parity", CM_UART_PARITY_NONE);
                uart->pack_timeout = get_json_int_val(item, "packTimeout", 25);
                uart->pack_max_len = get_json_int_val(item, "packMaxLen", 0);
                uart->pack_end_str = get_json_str_val(item, "packEndStr", NULL);
                uart->pull_interval = get_json_int_val(item, "interval", 0);

                // 解析自动轮询指令
                cJSON* cmd_list = cJSON_GetObjectItem(item, "cmdList");
                int cmd_len = cJSON_GetArraySize(cmd_list);
                int cmd_cnt = 0;
                for (int j = 0; j < cmd_len; j++)
                {
                    cJSON* cmd_item = cJSON_GetArrayItem(cmd_list, j);
                    if (cmd_item && cmd_item->type == cJSON_Object)
                    {
                        dtu_cmd_t* cmd = (dtu_cmd_t*)cm_malloc(sizeof(dtu_cmd_t));
                        if (cmd == NULL)
                        {
                            console_log("[dtu config] parse config cmd malloc error");
                            return RES_ERROR;
                        }
                        memset(cmd, 0, sizeof(dtu_cmd_t));

                        cmd->cmd = get_json_str_val(cmd_item, "cmd", NULL);
                        cmd->crc = get_json_bool_val(cmd_item, "crc", 0);
                        uart->cmd_list[cmd_cnt] = cmd;
                        cmd_cnt++;
                        if (cmd_cnt >= 10)
                            break;
                    }
                }

                uart->cmd_cnt = cmd_cnt;
                config->uart_list[uart_cnt] = uart;
                uart_cnt++;
                if (uart_cnt >= 3)
                    break;
            }
        }
        config->uart_cnt = uart_cnt;
    }

    // 发送参数
    cJSON* send_config = cJSON_GetObjectItem(root, "sendConfig");
    if (send_config && send_config->type == cJSON_Array)
    {
        int len = cJSON_GetArraySize(send_config);
        int channel_cnt = 0;
        for (int i = 0; i < len; i++)
        {
            cJSON* item = cJSON_GetArrayItem(send_config, i);
            if (item && item->type == cJSON_Object)
            {
                // 申请内存
                dtu_channel_t* channel = (dtu_channel_t*)cm_malloc(sizeof(dtu_channel_t));
                if (channel == NULL)
                {
                    console_log("[dtu config] parse config channel malloc error");
                    return RES_ERROR;
                }
                memset(channel, 0, sizeof(dtu_channel_t));

                // 解析参数
                channel->dev_num = get_json_int_val(item, "collectChannelNum", 1);
                channel->enabled = get_json_bool_val(item, "enabled", 0);
                channel->type = get_json_str_val(item, "type", "unknown");
                channel->recv_template = get_json_str_val(item, "recvTemplate", NULL);
                channel->send_template = get_json_str_val(item, "sendTemplate", NULL);

                cJSON* param = cJSON_GetObjectItem(item, "param");
                if (param && param->type == cJSON_Object)
                {
                    // TCP/UDP
                    if (strcmp(channel->type, "tcp") == 0 || strcmp(channel->type, "udp") == 0)
                    {
                        // 申请内存
                        dtu_socket_param_t* socket_param = (dtu_socket_param_t*)cm_malloc(sizeof(dtu_socket_param_t));
                        if (socket_param == NULL)
                        {
                            console_log("[dtu config] parse config socket param malloc error");
                            return RES_ERROR;
                        }
                        memset(socket_param, 0, sizeof(dtu_socket_param_t));

                        // 解析参数
                        socket_param->host = get_json_str_val(param, "host", "unknown");
                        socket_param->port = get_json_int_val(param, "port", 8080);
                        socket_param->heart_interval = get_json_int_val(param, "heartInterval", 0);
                        socket_param->heart_content_hex_mode = get_json_bool_val(param, "heartContentHexMode", 0);
                        socket_param->heart_content_template = get_json_str_val(param, "heartContentTemplate", NULL);

                        cJSON* ssl = cJSON_GetObjectItem(param, "ssl");
                        if (ssl && ssl->type == cJSON_Object)
                        {
                            socket_param->ssl_enable = get_json_bool_val(ssl, "enable", 0);
                            socket_param->ca_cert = get_json_str_val(ssl, "caCert", NULL);
                        }

                        channel->param = socket_param;
                    }
                    // MQTT
                    else if (strcmp(channel->type, "mqtt") == 0)
                    {
                        // 申请内存
                        dtu_mqtt_param_t* mqtt_param = (dtu_mqtt_param_t*)cm_malloc(sizeof(dtu_mqtt_param_t));
                        if (mqtt_param == NULL)
                        {
                            console_log("[dtu config] parse config mqtt param malloc error");
                            return RES_ERROR;
                        }
                        memset(mqtt_param, 0, sizeof(dtu_mqtt_param_t));

                        // 解析参数
                        mqtt_param->host = get_json_str_val(param, "host", "unknown");
                        mqtt_param->port = get_json_int_val(param, "port", 1883);
                        mqtt_param->client_id = get_json_str_val(param, "clientId", NULL);
                        mqtt_param->username = get_json_str_val(param, "username", NULL);
                        mqtt_param->password = get_json_str_val(param, "password", NULL);
                        mqtt_param->clean_session = get_json_bool_val(param, "cleanSession", 0);
                        mqtt_param->heart_interval = get_json_int_val(param, "heartInterval", 60000);
                        mqtt_param->sub_topic = get_json_str_val(param, "subTopic", NULL);
                        mqtt_param->pub_topic = get_json_str_val(param, "pubTopic", NULL);
                        mqtt_param->qos = get_json_int_val(param, "qos", 0);
                        mqtt_param->pub_retain = get_json_bool_val(param, "pubRetain", 0);

                        cJSON* last_will = cJSON_GetObjectItem(param, "lastWill");
                        if (last_will && last_will->type == cJSON_Object)
                        {
                            mqtt_param->last_will_enable = get_json_bool_val(param, "enabled", 0);
                            mqtt_param->last_will_topic = get_json_str_val(last_will, "topic", NULL);
                            mqtt_param->last_will_qos = get_json_int_val(last_will, "qos", 0);
                            mqtt_param->last_will_retain = get_json_int_val(last_will, "retain", 0);
                            mqtt_param->last_will_content = get_json_str_val(last_will, "content", NULL);
                        }

                        cJSON* ssl = cJSON_GetObjectItem(param, "ssl");
                        if (ssl && ssl->type == cJSON_Object)
                        {
                            mqtt_param->ssl_enable = get_json_bool_val(ssl, "enable", 0);
                            mqtt_param->ca_cert = get_json_str_val(ssl, "caCert", NULL);
                        }
                        channel->param = mqtt_param;
                    }
                    // HTTP
                    else if (strcmp(channel->type, "http") == 0)
                    {
                        // 申请内存
                        dtu_http_param_t* http_param = (dtu_http_param_t*)cm_malloc(sizeof(dtu_http_param_t));
                        if (http_param == NULL)
                        {
                            console_log("[dtu config] parse config http param malloc error");
                            return RES_ERROR;
                        }
                        memset(http_param, 0, sizeof(dtu_http_param_t));

                        // 解析参数
                        http_param->server = get_json_str_val(param, "server", "unknown");
                        http_param->path = get_json_str_val(param, "path", "/");
                        http_param->method = get_json_str_val(param, "method", "POST");
                        http_param->timeout = get_json_int_val(param, "timeout", HTTPCLIENT_CONNECT_TIMEOUT_DEFAULT * 1000);
                        http_param->request_type = get_json_str_val(param, "requestType", "inBody");
                        http_param->content_type = get_json_str_val(param, "contentType", "text/plain");

                        cJSON* headers = cJSON_GetObjectItem(param, "headers");
                        if (headers && headers->type == cJSON_Object)
                        {
                            // 将headers对象中所有键值对保存到动态列表中
                            int header_cnt = cJSON_GetArraySize(headers);
                            dynamic_list_t* header_list = dynamic_list_init(sizeof(char*));
                            for (int i = 0; i < header_cnt; i++)
                            {
                                cJSON* header = cJSON_GetArrayItem(headers, i);
                                if (header && header->type == cJSON_String)
                                {
                                    char* key = header->string;
                                    size_t key_len = strlen(key);
                                    char* value = header->valuestring;
                                    size_t value_len = strlen(value);
                                    char* header_item_str = cm_malloc(key_len + value_len + 3);
                                    memcpy(header_item_str, key, key_len);
                                    header_item_str[key_len] = ':';
                                    header_item_str[key_len + 1] = ' ';
                                    memcpy(header_item_str + key_len + 2, value, value_len);
                                    header_item_str[key_len + value_len + 2] = '\0';
                                    dynamic_list_add(header_list, header_item_str);
                                }
                            }

                            // 将动态列表中所有header用换行符连接起来，并赋给http_param->header
                            if (header_list && header_list->size > 0)
                            {
                                // 计算动态列表中所有header的长度
                                size_t header_str_len = 0;
                                for (int i = 0; i < header_list->size; i++)
                                {
                                    char* header_item_str = (char*)dynamic_list_get(header_list, i);
                                    header_str_len += strlen(header_item_str) + 2;
                                }

                                // 为header申请内存
                                char* header_str = cm_malloc(header_str_len + 1);
                                if (header_str == NULL)
                                {
                                    console_log("[dtu config] parse config http param header malloc error");
                                    return RES_ERROR;
                                }

                                // 将动态列表中所有header复制到header_str中
                                char* header_str_ptr = header_str;
                                for (int i = 0; i < header_list->size; i++)
                                {
                                    char* header_item_str = (char*)dynamic_list_get(header_list, i);
                                    size_t header_item_str_len = strlen(header_item_str);
                                    memcpy(header_str_ptr, header_item_str, header_item_str_len);
                                    header_str_ptr[header_item_str_len] = '\r';
                                    header_str_ptr[header_item_str_len + 1] = '\n';
                                    header_str_ptr += header_item_str_len + 2;
                                    cm_free(header_item_str);
                                }
                                header_str[header_str_len] = '\0';
                                http_param->header = header_str;
                                dynamic_list_free(header_list);
                            }
                        }

                        cJSON* ssl = cJSON_GetObjectItem(param, "ssl");
                        if (ssl && ssl->type == cJSON_Object)
                        {
                            http_param->ssl_enable = get_json_bool_val(ssl, "enable", 0);
                            http_param->ca_cert = get_json_str_val(ssl, "caCert", NULL);
                        }
                        channel->param = http_param;
                    }
                }

                config->channel_list[channel_cnt] = channel;
                channel_cnt++;
                if (channel_cnt >= 5)
                    break;
            }
        }
        config->channel_cnt = channel_cnt;
    }

    // 释放JSON对象
    cJSON_Delete(root);

    console_log("[dtu config] parse config success");

    return RES_SUCCESS;
}
