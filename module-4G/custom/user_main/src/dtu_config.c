#include "common.h"
#include "dtu_config.h"
#include "my_util.h"
#include "cm_uart.h"
#include "cm_sys.h"
#include "stdio.h"
#include "stdlib.h"
#include "stdarg.h"
#include "cm_os.h"
#include "cm_mem.h"
#include "cm_http.h"
#include "cm_ssl.h"
#include "cJSON.h"
#include "cm_fs.h"
#include "cm_sim.h"

// 函数向前声明
char* get_local_config_content();
char* get_remote_config_version(cm_httpclient_handle_t client);
char* get_remote_config_content(cm_httpclient_handle_t client);
char* get_remote_config_request_body();
uint8_t get_json_bool_val(cJSON* json, char* key, u_int8_t default_val);
uint32_t get_json_int_val(cJSON* json, char* key, uint32_t default_val);
char* get_json_str_val(cJSON* json, char* key, char* default_val);

/**
 * @brief 获取DTU配置
 *
 * @param config 配置结构体指针
 * @return COMMON_RES_T 获取结果
 */
COMMON_RES_T get_dtu_config(dtu_config_t* config)
{
    console_log("[dtu config] get config start");

    COMMON_RES_T res = RES_ERROR;

    // 获取本地配置
    char* local_version = NULL;
    char* remote_version = NULL;
    char* local_json_str = get_local_config_content();
    if (local_json_str != NULL)
    {
        cJSON* root = cJSON_Parse(local_json_str);
        if (root != NULL)
        {
            local_version = get_json_str_val(root, "version", "unknown");
            cJSON_Delete(root);
        }
    }

    console_log("[dtu config] local version: %s", local_version);

    // 创建http客户端
    cm_httpclient_handle_t client = NULL;
    cm_httpclient_ret_code_e ret = CM_HTTP_RET_CODE_UNKNOWN_ERROR;
    ret = cm_httpclient_create((const u_int8_t*)CONFIG_SERVER, NULL, &client);
    if (CM_HTTP_RET_CODE_OK != ret || client == NULL)
    {
        console_log("[dtu config] get config end, create httpclient error");
        goto end;
    }

    // 设置客户端参数
    cm_httpclient_cfg_t client_cfg;
    client_cfg.ssl_enable = CONFIG_SSL_ENABLE;                    // 使用SSL，即HTTPS连接方式。使用HTTP方式时该值为false
    client_cfg.ssl_id = 0;                                        // 设置SSL索引号
    client_cfg.cid = 0;                                           // 设置PDP索引号，目前不支持该项设置，设置任意值即可
    client_cfg.conn_timeout = HTTPCLIENT_CONNECT_TIMEOUT_DEFAULT; // 设置连接超时时间
    client_cfg.rsp_timeout = HTTPCLIENT_WAITRSP_TIMEOUT_DEFAULT;  // 设置响应超时时间
    client_cfg.dns_priority = 1;                                  // 设置DNS解析优先级，ipv6解析优先
    ret = cm_httpclient_set_cfg(client, client_cfg);
    if (CM_HTTP_RET_CODE_OK != ret)
    {
        console_log("[dtu config] get remote config httpclient cfg error");
        goto end;
    }

    // 设置SSL信息
    if (CONFIG_SSL_ENABLE)
    {
        int verify = 1;
        char* ca_cert = CONFIG_SERVER_CERTIFICATE;
        cm_ssl_setopt(0, CM_SSL_PARAM_VERIFY, &verify);   // 设置SSL验证方式
        cm_ssl_setopt(0, CM_SSL_PARAM_CA_CERT, ca_cert);  // 设置CA证书
    }
    else
    {
        int verify = 0;
        cm_ssl_setopt(0, CM_SSL_PARAM_VERIFY, &verify);      // 设置SSL验证方式
        cm_ssl_setopt(0, CM_SSL_PARAM_CA_CERT_DELETE, NULL); // 删除CA证书
    }

    // 获取远程版本
    remote_version = get_remote_config_version(client);
    if (remote_version == NULL)
    {
        console_log("[dtu config] get config end, get remote version error");
        goto end;
    }

    // 如果本地版本和远程版本一致，则不需要更新
    if (local_version != NULL && strcmp(remote_version, local_version) == 0)
    {
        res = parse_dtu_config(local_json_str, config);
        goto end;
    }
    // 拉取远程配置并更新到本地
    else
    {
        // 拉取远程配置内容
        char* config_json_str = get_remote_config_content(client);
        if (config_json_str == NULL)
        {
            goto end;
        }

        // 解析配置内容
        res = parse_dtu_config(config_json_str, config);

        console_log("config_json_str: %s\n", config_json_str);

        // 如果解析失败，则返回错误
        if (res == RES_ERROR)
        {
            free_ptr(config_json_str);
            goto end;
        }

        // 如果本地配置文件不存在，则保存
        if (!cm_fs_exist(LOCAL_CONFIG_FILE_NAME))
        {
            console_log("[dtu config] local config not exist, save");
            int32_t fd = cm_fs_open(LOCAL_CONFIG_FILE_NAME, CM_FS_WB);
            cm_fs_write(fd, config_json_str, strlen(config_json_str));
            cm_fs_close(fd);
        }
        // 如果本地配置文件存在，且内容不一致，则更新
        else
        {
            console_log("[dtu config] local config not equal, update");
            cm_fs_delete(LOCAL_CONFIG_FILE_NAME);
            int32_t fd = cm_fs_open(LOCAL_CONFIG_FILE_NAME, CM_FS_WB);
            cm_fs_write(fd, config_json_str, strlen(config_json_str));
            cm_fs_close(fd);
        }
    }

end:
    // 释放内存
    free_ptr(local_version);
    free_ptr(remote_version);
    free_ptr(local_json_str);
    // 释放客户端
    if (client != NULL)
    {
        cm_httpclient_terminate(client);
        cm_httpclient_delete(client);
    }
    return res;
}

/**
 * @brief 获取远程配置版本
 *
 * @param client 客户端
 * @return char*
 */
char* get_remote_config_version(cm_httpclient_handle_t client)
{
    console_log("[dtu config] get remote config version start");

    char* request_body = get_remote_config_request_body();
    if (request_body == NULL)
    {
        console_log("[dtu config] get remote config version failed, request body is null");
        return NULL;
    }

    char* content_type = "Content-Type: application/json";
    cm_httpclient_custom_header_set(client, (uint8_t*)content_type, strlen(content_type));

    cm_httpclient_sync_response_t response = {};
    cm_httpclient_sync_param_t param = { HTTPCLIENT_REQUEST_POST, (const uint8_t*)CONFIG_SERVER_CONFIG_VERSION_URI, strlen(request_body), (uint8_t*)request_body };
    cm_httpclient_ret_code_e ret = cm_httpclient_sync_request(client, param, &response);

    // 请求失败
    if (CM_HTTP_RET_CODE_OK != ret)
    {
        console_log("[dtu config] get remote config version request error, ret=%d", ret);
        free_ptr(request_body);
        return NULL;
    }

    // 解析json
    cJSON* root = cJSON_Parse((char*)response.response_content);
    if (!root)
    {
        console_log("[dtu config] get remote config version failed, parse json error");
    }

    // 判断接口是否成功
    int code = get_json_int_val(root, "code", 500);
    if (code != 200)
    {
        char* msg = get_json_str_val(root, "msg", "unknown error");
        console_log("[dtu config] get remote config version failed, code=%d, msg=%s", code, msg);

        // 释放资源
        free_ptr(msg);
        free_ptr(request_body);
        cJSON_Delete(root);
        cm_httpclient_sync_free_data(client);
        return NULL;
    }

    char* version = get_json_str_val(root, "data", "1.0.0");

    // 释放资源
    free_ptr(request_body);
    cJSON_Delete(root);
    cm_httpclient_sync_free_data(client);

    console_log("[dtu config] get remote config version success");

    return version;
}

/**
 * @brief 获取远程配置内容
 *
 * @return char* 配置内容
 */
char* get_remote_config_content(cm_httpclient_handle_t client)
{
    console_log("[dtu config] get remote config content start");

    char* request_body = get_remote_config_request_body();
    if (request_body == NULL)
    {
        console_log("[dtu config] get remote config content failed, request body is null");
        return NULL;
    }

    char* content_type = "Content-Type: application/json";
    cm_httpclient_custom_header_set(client, (uint8_t*)content_type, strlen(content_type));

    cm_httpclient_sync_response_t response = {};
    cm_httpclient_sync_param_t param = { HTTPCLIENT_REQUEST_POST, (const uint8_t*)CONFIG_SERVER_CONFIG_GET_URI, strlen(request_body), (uint8_t*)request_body };
    cm_httpclient_ret_code_e ret = cm_httpclient_sync_request(client, param, &response);

    // 请求失败
    if (CM_HTTP_RET_CODE_OK != ret)
    {
        console_log("[dtu config] get remote config content request error, ret=%d", ret);
        free_ptr(request_body);
        return NULL;
    }

    // 解析json
    cJSON* root = cJSON_Parse((char*)response.response_content);
    if (!root)
    {
        console_log("[dtu config] get remote config content failed, parse json error");
    }

    // 判断接口是否成功
    int code = get_json_int_val(root, "code", 500);
    if (code != 200)
    {
        char* msg = get_json_str_val(root, "msg", "unknown error");
        console_log("[dtu config] get remote config content failed, code=%d, msg=%s", code, msg);

        // 释放资源
        free_ptr(msg);
        free_ptr(request_body);
        cJSON_Delete(root);
        cm_httpclient_sync_free_data(client);
        return NULL;
    }

    // 获取data对象
    cJSON* data = cJSON_GetObjectItem(root, "data");
    if (data == NULL)
    {
        console_log("[dtu config] get remote config content failed, data is null");

        // 释放资源
        free_ptr(request_body);
        cJSON_Delete(root);
        cm_httpclient_sync_free_data(client);
        return NULL;
    }

    char* json_str = cJSON_PrintUnformatted(data);

    // 释放资源
    free_ptr(request_body);
    cJSON_Delete(root);
    cm_httpclient_sync_free_data(client);

    console_log("[dtu config] get remote config content success");

    return json_str;
}

/**
 * @brief 获取远程配置请求体
 *
 * @return char* 请求内容
 */
char* get_remote_config_request_body()
{
    char imei[CM_IMEI_LEN] = { 0 };
    memset(imei, 0, CM_IMEI_LEN);
    int ret = cm_sys_get_imei(imei);
    if (ret != 0)
    {
        console_log("[dtu config] get remote request body failed, get imei failed");
        return NULL;
    }

    char iccid[21] = { 0 };
    memset(iccid, 0, 21);
    ret = cm_sim_get_iccid(iccid);
    if (ret != 0)
    {
        console_log("[dtu config] get remote request body failed, get iccid failed");
        return NULL;
    }

    cJSON* root = cJSON_CreateObject();
    cJSON_AddStringToObject(root, "deviceImei", imei);
    cJSON_AddStringToObject(root, "simIccid", iccid);

    char* body = cJSON_PrintUnformatted(root);

    console_log("[dtu config] get remote request body: %s", body);

    cJSON_Delete(root);

    return body;
}

/**
 * @brief 获取本地配置信息
 *
 * @return char* 配置内容
 */
char* get_local_config_content()
{
    console_log("[dtu config] get local config start");
    char* local_json_str = NULL;

    if (cm_fs_exist(LOCAL_CONFIG_FILE_NAME))
    {
        // 打开文件
        int32_t fd = cm_fs_open(LOCAL_CONFIG_FILE_NAME, CM_FS_RB);
        if (fd < 0)
        {
            console_log("[dtu config] get local config open file error");
            return NULL;
        }

        // 获取文件大小
        int32_t file_size = cm_fs_filesize(LOCAL_CONFIG_FILE_NAME);
        if (file_size < 1)
        {
            console_log("[dtu config] get local config file size error");
            cm_fs_close(fd);
            return NULL;
        }

        // 申请内存
        local_json_str = cm_malloc(file_size + 1);
        if (local_json_str == NULL)
        {
            console_log("[dtu config] get local config malloc error");
            cm_fs_close(fd);
            return NULL;
        }

        // 读取文件内容
        cm_fs_read(fd, local_json_str, file_size);
        // 关闭文件
        cm_fs_close(fd);

        console_log("[dtu config] get local config success");

        // 确保字符串结束
        if (local_json_str != NULL)
        {
            local_json_str[file_size] = '\0';
        }
    }
    else
    {
        console_log("[dtu config] get local config file not exist");
    }

    return local_json_str;
}

/**
 * @brief 获取JSON布尔值
 *
 * @param json JSON对象
 * @param key 键
 * @param default_val 默认值
 * @return uint8_t 布尔值
 */
uint8_t get_json_bool_val(cJSON* json, char* key, u_int8_t default_val)
{
    cJSON* item = cJSON_GetObjectItem(json, key);
    if (item)
    {
        return item->type == cJSON_True ? 1 : 0;
    }
    return default_val;
}

/**
 * @brief 获取JSON整数值
 *
 * @param json JSON对象
 * @param key 键
 * @param default_val 默认值
 * @return uint32_t 整数值
 */
uint32_t get_json_int_val(cJSON* json, char* key, uint32_t default_val)
{
    cJSON* item = cJSON_GetObjectItem(json, key);
    if (item && item->type == cJSON_Number)
    {
        return item->valueint;
    }
    return default_val;
}

/**
 * @brief 获取JSON字符串
 *
 * @param json JSON对象
 * @param key 键
 * @param default_val 默认值
 * @return char* 字符串
 */
char* get_json_str_val(cJSON* json, char* key, char* default_val)
{
    cJSON* item = cJSON_GetObjectItem(json, key);
    if (item && item->type == cJSON_String)
    {
        return copy_str(item->valuestring);
    }
    return default_val != NULL ? copy_str(default_val) : NULL;
}

/**
 * @brief 解析配置
 *
 * @param config_json_str 配置内容
 * @param config 配置结构体指针
 * @return COMMON_RES_T 解析结果
 */
COMMON_RES_T parse_dtu_config(char* config_json_str, dtu_config_t* config)
{
    console_log("[dtu config] parse config start");

    cJSON* root = cJSON_Parse(config_json_str);
    if (!root)
    {
        console_log("[dtu config] parse config error before: [%s]", cJSON_GetErrorPtr());
        return RES_ERROR;
    }

    config->version = get_json_str_val(root, "version", "unknow");
    config->device_imei = get_json_str_val(root, "deviceImei", "unknow");
    config->channel_list = dynamic_list_init(sizeof(dtu_channel_t*));

    // 通道列表
    cJSON* channel_list = cJSON_GetObjectItem(root, "channelList");
    if (channel_list && channel_list->type == cJSON_Array)
    {
        int len = cJSON_GetArraySize(channel_list);
        for (int i = 0; i < len; i++)
        {
            cJSON* item = cJSON_GetArrayItem(channel_list, i);
            if (item && item->type == cJSON_Object)
            {

                // 申请内存
                dtu_channel_t* channel = (dtu_channel_t*)cm_malloc(sizeof(dtu_channel_t));
                if (channel == NULL)
                {
                    console_log("[dtu config] parse config channel malloc error");
                    return RES_ERROR;
                }
                memset(channel, 0, sizeof(dtu_channel_t));

                channel->enable = get_json_bool_val(item, "enable", 0);
                channel->protocol = get_json_str_val(item, "protocol", "unknown");
                channel->collect_channel_num = get_json_int_val(item, "collectChannelNum", 0);
                channel->type = get_json_str_val(item, "type", "unknown");

                cJSON* param = cJSON_GetObjectItem(item, "param");
                if (param && param->type == cJSON_Object)
                {
                    // rs485/rs232
                    if (strcmp(channel->protocol, "rs485/rs232") == 0)
                    {
                        // 申请内存
                        dtu_uart_param_t* uart = (dtu_uart_param_t*)cm_malloc(sizeof(dtu_uart_param_t));
                        if (uart == NULL)
                        {
                            console_log("[dtu config] parse config uart malloc error");
                            return RES_ERROR;
                        }
                        memset(uart, 0, sizeof(dtu_uart_param_t));

                        // 解析参数
                        uart->baud_rate = get_json_int_val(param, "baudRate", CM_UART_BAUDRATE_9600);
                        uart->byte_size = get_json_int_val(param, "byteSize", CM_UART_BYTE_SIZE_8);
                        uart->stop_bit = get_json_int_val(param, "stopBit", CM_UART_STOP_BIT_ONE);
                        uart->parity = get_json_int_val(param, "parity", CM_UART_PARITY_NONE);
                        uart->pack_timeout = get_json_int_val(param, "packTimeout", 25);
                        uart->pack_max_len = get_json_int_val(param, "packMaxLen", 0);
                        uart->collect_interval = get_json_int_val(param, "collectInterval", 0);
                        uart->cmd_list = dynamic_list_init(sizeof(dtu_cmd_t*));

                        // 解析自动轮询指令
                        cJSON* cmd_list = cJSON_GetObjectItem(param, "cmdList");
                        int cmd_len = cJSON_GetArraySize(cmd_list);
                        for (int j = 0; j < cmd_len; j++)
                        {
                            cJSON* cmd_item = cJSON_GetArrayItem(cmd_list, j);
                            if (cmd_item && cmd_item->type == cJSON_Object)
                            {
                                dtu_cmd_t* cmd = (dtu_cmd_t*)cm_malloc(sizeof(dtu_cmd_t));
                                if (cmd == NULL)
                                {
                                    console_log("[dtu config] parse config cmd malloc error");
                                    return RES_ERROR;
                                }
                                memset(cmd, 0, sizeof(dtu_cmd_t));

                                cmd->cmd = get_json_str_val(cmd_item, "cmd", NULL);
                                cmd->crc = get_json_bool_val(cmd_item, "crc", 0);
                                dynamic_list_add(uart->cmd_list, cmd);
                            }
                        }

                        channel->param = uart;
                    }
                    // TCP/UDP
                    else if (strcmp(channel->protocol, "tcp") == 0 || strcmp(channel->protocol, "udp") == 0)
                    {
                        // 申请内存
                        dtu_socket_param_t* socket_param = (dtu_socket_param_t*)cm_malloc(sizeof(dtu_socket_param_t));
                        if (socket_param == NULL)
                        {
                            console_log("[dtu config] parse config socket param malloc error");
                            return RES_ERROR;
                        }
                        memset(socket_param, 0, sizeof(dtu_socket_param_t));

                        // 解析参数
                        socket_param->host = get_json_str_val(param, "host", "unknown");
                        socket_param->port = get_json_int_val(param, "port", 8080);
                        socket_param->heart_interval = get_json_int_val(param, "heartInterval", 0);
                        socket_param->heart_type = get_json_str_val(param, "heartType", "string");
                        socket_param->heart_content = get_json_str_val(param, "heartContent", NULL);

                        socket_param->ssl_enable = get_json_bool_val(param, "sslEnable", 0);
                        socket_param->ssl_ca_cert = get_json_str_val(param, "sslCaCert", NULL);

                        channel->receive_template = get_json_str_val(param, "receiveTemplate", NULL);
                        channel->send_template = get_json_str_val(param, "sendTemplate", NULL);

                        channel->param = socket_param;
                    }
                    // MQTT
                    else if (strcmp(channel->protocol, "mqtt") == 0)
                    {
                        // 申请内存
                        dtu_mqtt_param_t* mqtt_param = (dtu_mqtt_param_t*)cm_malloc(sizeof(dtu_mqtt_param_t));
                        if (mqtt_param == NULL)
                        {
                            console_log("[dtu config] parse config mqtt param malloc error");
                            return RES_ERROR;
                        }
                        memset(mqtt_param, 0, sizeof(dtu_mqtt_param_t));

                        // 解析参数
                        mqtt_param->host = get_json_str_val(param, "host", "unknown");
                        mqtt_param->port = get_json_int_val(param, "port", 1883);
                        mqtt_param->heart_interval = get_json_int_val(param, "heartInterval", 60000);
                        mqtt_param->client_id = get_json_str_val(param, "clientId", NULL);
                        mqtt_param->username = get_json_str_val(param, "username", NULL);
                        mqtt_param->password = get_json_str_val(param, "password", NULL);
                        mqtt_param->clean_session = get_json_bool_val(param, "cleanSession", 0);
                        mqtt_param->pub_retain = get_json_bool_val(param, "pubRetain", 0);
                        mqtt_param->pub_qos = get_json_int_val(param, "pubQos", 0);
                        mqtt_param->sub_qos = get_json_int_val(param, "subQos", 0);
                        mqtt_param->sub_topic = get_json_str_val(param, "subTopic", NULL);
                        mqtt_param->pub_topic = get_json_str_val(param, "pubTopic", NULL);
                        mqtt_param->last_will_enable = get_json_bool_val(param, "lastWillEnable", 0);
                        mqtt_param->last_will_qos = get_json_int_val(param, "lastWillQos", 0);
                        mqtt_param->last_will_retain = get_json_int_val(param, "lastWillRetain", 0);
                        mqtt_param->last_will_topic = get_json_str_val(param, "lastWillTopic", NULL);
                        mqtt_param->last_will_content = get_json_str_val(param, "lastWillContent", NULL);

                        mqtt_param->ssl_enable = get_json_bool_val(param, "sslEnable", 0);
                        mqtt_param->ssl_ca_cert = get_json_str_val(param, "sslCaCert", NULL);

                        channel->receive_template = get_json_str_val(param, "receiveTemplate", NULL);
                        channel->send_template = get_json_str_val(param, "sendTemplate", NULL);

                        channel->param = mqtt_param;
                    }
                    // HTTP
                    else if (strcmp(channel->protocol, "http") == 0)
                    {
                        // 申请内存
                        dtu_http_param_t* http_param = (dtu_http_param_t*)cm_malloc(sizeof(dtu_http_param_t));
                        if (http_param == NULL)
                        {
                            console_log("[dtu config] parse config http param malloc error");
                            return RES_ERROR;
                        }
                        memset(http_param, 0, sizeof(dtu_http_param_t));

                        // 解析参数
                        http_param->server = get_json_str_val(param, "server", "unknown");
                        http_param->path = get_json_str_val(param, "path", "/");
                        http_param->method = get_json_str_val(param, "method", "POST");
                        http_param->timeout = get_json_int_val(param, "timeout", HTTPCLIENT_CONNECT_TIMEOUT_DEFAULT * 1000);
                        http_param->request_type = get_json_str_val(param, "requestType", "inBody");
                        http_param->content_type = get_json_str_val(param, "contentType", "text/plain");

                        cJSON* headers = cJSON_GetObjectItem(param, "headers");
                        if (headers && headers->type == cJSON_Object)
                        {
                            // 将headers对象中所有键值对保存到动态列表中
                            int header_cnt = cJSON_GetArraySize(headers);
                            dynamic_list_t* header_list = dynamic_list_init(sizeof(char*));
                            for (int i = 0; i < header_cnt; i++)
                            {
                                cJSON* header = cJSON_GetArrayItem(headers, i);
                                if (header && header->type == cJSON_String)
                                {
                                    char* key = header->string;
                                    size_t key_len = strlen(key);
                                    char* value = header->valuestring;
                                    size_t value_len = strlen(value);
                                    char* header_item_str = cm_malloc(key_len + value_len + 3);
                                    memcpy(header_item_str, key, key_len);
                                    header_item_str[key_len] = ':';
                                    header_item_str[key_len + 1] = ' ';
                                    memcpy(header_item_str + key_len + 2, value, value_len);
                                    header_item_str[key_len + value_len + 2] = '\0';
                                    dynamic_list_add(header_list, header_item_str);
                                }
                            }

                            // 将动态列表中所有header用换行符连接起来，并赋给http_param->header
                            if (header_list && header_list->size > 0)
                            {
                                // 计算动态列表中所有header的长度
                                size_t header_str_len = 0;
                                for (int i = 0; i < header_list->size; i++)
                                {
                                    char* header_item_str = (char*)dynamic_list_get(header_list, i);
                                    header_str_len += strlen(header_item_str) + 2;
                                }

                                // 为header申请内存
                                char* header_str = cm_malloc(header_str_len + 1);
                                if (header_str == NULL)
                                {
                                    console_log("[dtu config] parse config http param header malloc error");
                                    return RES_ERROR;
                                }

                                // 将动态列表中所有header复制到header_str中
                                char* header_str_ptr = header_str;
                                for (int i = 0; i < header_list->size; i++)
                                {
                                    char* header_item_str = (char*)dynamic_list_get(header_list, i);
                                    size_t header_item_str_len = strlen(header_item_str);
                                    memcpy(header_str_ptr, header_item_str, header_item_str_len);
                                    header_str_ptr[header_item_str_len] = '\r';
                                    header_str_ptr[header_item_str_len + 1] = '\n';
                                    header_str_ptr += header_item_str_len + 2;
                                    free_ptr(header_item_str);
                                }
                                header_str[header_str_len] = '\0';
                                http_param->headers = header_str;
                                dynamic_list_free(header_list);
                            }
                        }

                        http_param->ssl_enable = get_json_bool_val(param, "sslEnable", 0);
                        http_param->ssl_ca_cert = get_json_str_val(param, "sslCaCert", NULL);

                        channel->receive_template = get_json_str_val(param, "receiveTemplate", NULL);
                        channel->send_template = get_json_str_val(param, "sendTemplate", NULL);

                        channel->param = http_param;
                    }
                }

                dynamic_list_add(config->channel_list, channel);
            }
        }
    }

    // 释放JSON对象
    cJSON_Delete(root);

    console_log("[dtu config] parse config success");

    return RES_SUCCESS;
}
