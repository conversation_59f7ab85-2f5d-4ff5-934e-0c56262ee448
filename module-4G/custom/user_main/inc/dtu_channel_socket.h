#ifndef __DTU_CHANNEL_SOCKET_H__
#define __DTU_CHANNEL_SOCKET_H__ 

#include "common.h"
#include "dtu_config.h"
#include "cm_ssl.h"
#include "dtu_channel.h"

/**
 * @brief socket通道上下文结构体
 */
typedef struct
{
    dtu_socket_param_t* param;           // 连接参数
    uint8_t id;                          // 通道id
    int protocol;                        // 协议 IPPROTO_UDP/IPPROTO_TCP
    int sock;                            // socket句柄
    uint8_t connect_status;              // 0 未连接 1 连接中 2 握手中 3 已连接
    cm_ssl_ctx_t* ssl_ctx;               // ssl上下文
    osThreadId_t connect_thread_id;      // 连接线程id
    osSemaphoreId_t read_thread_sem;     // 读取数据线程信号量
    osThreadId_t read_thread_id;         // 读取数据线程id
    osThreadId_t pack_timer_id;          // 分包定时器id
    char* buff;                          // 缓冲区
    uint32_t buff_len;                   // 缓冲区大小
    osThreadId_t heart_timer_id;         // 心跳定时器id
    data_receive_callback_t callback;    // 接收回调
    osMessageQueueId_t send_queue_id;    // 发送队列id
    osThreadId_t send_thread_id;         // 发送线程id    
    osMessageQueueId_t consume_queue_id; // 消费队列id
    osThreadId_t consume_thread_id;      // 消费线程id    
} socket_channel_ctx_t;

/**
 * @brief socket通道初始化
 *
 * @param param socket参数
 * @param id 通道id
 * @param is_udp 是否udp
 * @param callback 接收回调
 * @return socket_channel_ctx_t* socket通道上下文
 */
socket_channel_ctx_t* socket_channel_init(dtu_socket_param_t* param, uint8_t id, uint8_t is_udp, data_receive_callback_t callback);

/**
 * @brief socket通道发送
 *
 * @param ctx socket通道上下文
 * @param data 数据
 * @param len 数据长度
 */
COMMON_RES_T socket_channel_send(socket_channel_ctx_t* ctx, gc_data_t* data, size_t len);

/**
 * @brief socket通道关闭
 *
 * @param ctx socket通道上下文
 */
void socket_channel_close(socket_channel_ctx_t* ctx);

#endif