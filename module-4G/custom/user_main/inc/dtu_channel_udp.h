#ifndef __DTU_CHANNEL_UDP_H__
#define __DTU_CHANNEL_UDP_H__ 

#include "common.h"
#include "dtu_config.h"
#include "cm_ssl.h"
#include "dtu_channel.h"

/**
 * @brief udp通道上下文结构体
 */
typedef struct
{
    dtu_socket_param_t* param;           // 连接参数
    uint8_t id;                          // 通道id
    int sock;                            // socket句柄
    uint8_t connect_status;              // 0 未连接 1 已连接
    osThreadId_t read_thread_id;         // 读取数据线程id
    osThreadId_t pack_timer_id;          // 分包定时器id
    char* buff;                          // 缓冲区
    uint32_t buff_len;                   // 缓冲区大小
    osThreadId_t heart_thread_id;        // 心跳线程id
    data_receive_callback_t callback;    // 接收回调
    osMessageQueueId_t send_queue_id;    // 发送队列id
    osThreadId_t send_thread_id;         // 发送线程id    
    osMessageQueueId_t consume_queue_id; // 消费队列id
    osThreadId_t consume_thread_id;      // 消费线程id    
} udp_channel_ctx_t;

/**
 * @brief udp通道初始化
 *
 * @param param socket参数
 * @param id 通道id
 * @param is_udp 是否udp
 * @param callback 接收回调
 * @return udp_channel_ctx_t* udp通道上下文
 */
udp_channel_ctx_t* udp_channel_init(dtu_socket_param_t* param, uint8_t id, data_receive_callback_t callback);

/**
 * @brief udp通道发送
 *
 * @param ctx u'dp上下文
 * @param data 数据
 * @param len 数据长度
 */
COMMON_RES_T udp_channel_send(udp_channel_ctx_t* ctx, gc_data_t* data, size_t len);

/**
 * @brief udp通道关闭
 *
 * @param ctx udp通道上下文
 */
void udp_channel_close(udp_channel_ctx_t* ctx);

#endif