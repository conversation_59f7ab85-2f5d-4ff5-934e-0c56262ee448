#ifndef __DTU_CHANNEL_HTTP_H__
#define __DTU_CHANNEL_HTTP_H__ 

#include "common.h"
#include "dtu_config.h"
#include "dtu_channel.h"
#include "cm_http.h"

/**
 * @brief http通道上下文结构体
 */
typedef struct
{
    dtu_http_param_t* param;             // 连接参数
    uint8_t id;                          // 通道id
    cm_httpclient_handle_t client;       // http客户端句柄  
    data_receive_callback_t callback;    // 接收回调
    osMessageQueueId_t send_queue_id;    // 发送队列id
    osThreadId_t send_thread_id;         // 发送线程id 
} http_channel_ctx_t;

/**
 * @brief http通道初始化
 *
 * @param param http参数
 * @param id 通道id
 * @param callback 接收回调
 * @return http_channel_ctx_t* http通道上下文
 */
http_channel_ctx_t* http_channel_init(dtu_http_param_t* param, uint8_t id, data_receive_callback_t callback);

/**
 * @brief http通道发送
 *
 * @param ctx http通道上下文
 * @param data 数据
 * @param len 数据长度
 */
COMMON_RES_T http_channel_send(http_channel_ctx_t* ctx, gc_data_t* data, size_t len);

/**
 * @brief http通道关闭
 *
 * @param ctx http通道上下文
 */
void http_channel_close(http_channel_ctx_t* ctx);

#endif