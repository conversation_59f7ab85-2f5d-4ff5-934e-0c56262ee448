#ifndef __DTU_CHANNEL_H__
#define __DTU_CHANNEL_H__ 

#include "common.h"
#include "my_util.h"
#include "dtu_config.h"
#include "pikaScript.h"

/**
 * @brief 通道信息结构体
 */
typedef struct
{
    uint8_t dev_num;      // 通道ID
    uint8_t id;           // 通道ID
    char* type;           // 通道类型
    void* ctx;            // 通道上下文
    dtu_channel_t* param; // 通道参数
} dtu_channel_info_t;

// 定义DTU接收回调函数
typedef void (*dtu_receive_callback_t)(dtu_channel_info_t* channel_info, gc_data_t* data, size_t len);

// 定义数据接收回调函数
typedef void (*data_receive_callback_t)(void* ctx, uint8_t* data, size_t len);

/**
 * @brief 注册通道回调函数
 *
 * @param callback 通道接收回调函数
 */
void dtu_channel_register_callback(dtu_receive_callback_t callback);

/**
 * @brief 注册通道
 *
 * @param channel 通道配置
 * @param callback 通道接收回调函数
 *
 * @return channel_info_t* 通道信息
 */
dtu_channel_info_t* dtu_channel_init(dtu_channel_t* channel_param);

/**
 * @brief 发送数据
 *
 * @param channel 通道句柄
 * @param data 数据
 * @param len 数据长度
 *
 * @return COMMON_RES_T
 */
COMMON_RES_T dtu_channel_send(dtu_channel_info_t* channel_info, gc_data_t* data, size_t len);

/**
 * @brief 关闭所有通道
 */
void dtu_channel_close_all(void);

#endif