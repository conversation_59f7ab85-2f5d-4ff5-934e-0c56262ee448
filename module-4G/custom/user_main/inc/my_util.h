#ifndef __MY_UTIL_H__
#define __MY_UTIL_H__

#include <stdint.h>
#include <string.h>
#include "cm_sys.h"

// 获取毫秒数
#define get_ms(num) num / 5

/**
 * @brief 通用消息结构体
 */
typedef struct
{
    uint8_t* data;
    size_t len;
} comm_msg_t;

/**
 * @brief 动态列表
 */
typedef struct
{
    void** data;
    uint32_t size;
    uint32_t element_size;
} dynamic_list_t;

/**
 * @brief 引用计数消息结构体，用于释放数据
 */
typedef struct
{
    void* data;
    uint32_t ref;
} gc_data_t;

/**
 * @brief 通用引用消息结构体
 */
typedef struct
{
    gc_data_t* data;
    size_t len;
} comm_gc_msg_t;

/**
 * @brief 创建GC任务
 */
void gc_task_init();

/**
 * @brief 创建GC数据
 * @param data 数据指针
 * @param ref 引用计数
 * @return GC数据指针
 */
gc_data_t* gc_data_init(void* data, uint32_t ref);

/**
 * @brief 尝试释放数据
 * @param msg 消息指针
 */
void gc_try_free(gc_data_t* msg);

/**
 * @brief 创建动态列表
 * @param size 列表大小
 * @return 列表指针
 */
dynamic_list_t* dynamic_list_init(uint32_t element_size);

/**
 * @brief 释放动态列表
 * @param list 列表指针
 */
void dynamic_list_free(dynamic_list_t* list);

/**
 * @brief 添加数据到动态列表
 * @param list 列表指针
 * @param data 数据指针
 */
void dynamic_list_add(dynamic_list_t* list, void* data);

/**
 * @brief 删除数据从动态列表
 * @param list 列表指针
 * @param data 数据指针
 */
void dynamic_list_remove(dynamic_list_t* list, void* data);

/**
 * @brief 查找数据在动态列表中的索引
 * @param list 列表指针
 * @param data 数据指针
 * @return 数据索引，未找到返回-1
 */
int dynamic_list_find(dynamic_list_t* list, void* data);

/**
 * @brief 根据索引获取数据
 * @param list 列表指针
 * @param index 索引
 * @return 数据指针
 */
void* dynamic_list_get(dynamic_list_t* list, uint32_t index);

/**
 * @brief 获取下一个ID
 * @return ID
 */
uint8_t next_global_id(void);

/**
 * @brief 重置ID
 * @param id ID
 */
void reset_global_id(uint8_t id);

/**
 * @brief 拷贝字符串
 * @param param 源字符串指针
 * @return 拷贝后的字符串指针
 */
char* copy_str(char* param);

/**
 * @brief 拷贝指定长度的字符串
 * @param param 源字符串指针
 * @param len 拷贝长度
 * @return 拷贝后的字符串指针
 */
char* copy_str_by_len(char* param, size_t len);

/**
 * @brief 串口打印
 */
void uart_printf(uint8_t dev_num, char* str, ...);

/**
 * @brief 十六进制字符串转字节数组
 * @param hex_str 十六进制字符串
 * @param buff 缓冲区
 * @param buff_len 缓冲区大小
 * @param crc 是否添加CRC校验
 * @return 字节数
 */
size_t hex_to_bytes(uint8_t* hex_str, uint8_t* buff, size_t buff_len, uint8_t crc);

/**
 * @brief 字节数组转十六进制字符串
 * @param bytes 字节数组
 * @param len 字节数
 * @return 十六进制字符串
 */
char* bytes_to_hex(uint8_t* bytes, size_t len);

/**
 * @brief 字符串替换
 *
 * @param str 原字符串
 * @param from 需要替换的子串
 * @param to 目标子串
 * @return 替换后的字符串
 */
char* str_replace(const char* str, const char* from, const char* to);

// #define console_log(fmt, ...) uart_printf(AT_UART_DEV_NUM, fmt, ##__VA_ARGS__)
#define console_log(fmt, ...) cm_log_printf(0, fmt, ##__VA_ARGS__)

#endif