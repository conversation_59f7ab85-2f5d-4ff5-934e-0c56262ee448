#ifndef __CONFIG_UART_H__
#define __CONFIG_UART_H__

#include <stdint.h>
#include "common.h"
#include "dtu_config.h"
#include "my_util.h"

/**
 * @brief 串口上下文结构体
 */
typedef struct
{
    dtu_uart_t* config;                // 串口配置信息
    uint8_t dev_num;                   // 串口设备编号
    osSemaphoreId_t read_sem;          // 串口读取信号量
    osThreadId_t read_thread_id;       // 读取线程
    osTimerId_t receive_pack_timer_id; // 接收数据分包定时器
    char* receive_buf;                 // 接收缓存
    uint32_t receive_buf_len;          // 缓存长度
    osTimerId_t auto_pull_thread_id;   // 自动拉取数据线程
} uart_ctx_t;

// 串口消费回调函数
typedef void (*uart_consume_callback_t)(uart_ctx_t* ctx, uint8_t* data, uint32_t len);

/**
 * @brief 串口消费任务结构体
 */
typedef struct
{
    osThreadId_t thread_id;             // 任务线程id
    osMessageQueueId_t msg_queue_id;    // 消息队列
    uart_consume_callback_t callback;   // 回调函数
    char* name;                         // 任务线程名称
} uart_consume_task_t;

/**
 * @brief 串口消费任务消息结构体
 */
typedef struct
{
    uart_ctx_t* ctx;  // 串口上下文
    gc_data_t* data;  // 数据
    uint32_t len;     // 数据长度
} uart_consume_task_msg_t;

/**
 * @brief 串口初始化
 * @param[in] config 配置信息
 * @param[out] ctx 串口上下文
 * @return 串口上下文，NULL则为初始化失败
 */
uart_ctx_t* uart_init(dtu_uart_t* config);

/**
 * @brief 串口释放
 * @param[in] dev_num 串口设备编号
 */
void uart_deinit(uint8_t dev_num);

/**
 * @brief 注册串口消费任务，同一个函数不会重复注册
 * @param[in] callback 回调函数
 * @param[in] name 任务名称
 * @param[in] stack_size 栈空间大小
 * @return 注册结果
 */
COMMON_RES_T uart_register_consume_task(uart_consume_callback_t callback, char* name, uint32_t stack_size);

/**
 * @brief 注销串口消费任务
 * @param[in] callback 回调函数
 * @return 注销结果
 */
COMMON_RES_T uart_unregister_consume_task(uart_consume_callback_t callback);

#endif