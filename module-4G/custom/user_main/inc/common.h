#ifndef __COMMON_H__
#define __COMMON_H__

#include <stdint.h>
#include <string.h>
#include "cm_os.h"
#include "cm_mem.h"
#include "cm_gpio.h"
#include "cm_iomux.h"

#ifndef FALSE
#define FALSE (0U)
#endif

#ifndef TRUE
#define TRUE (1U)
#endif

#define UART_TASK_PRIORITY osPriorityNormal

#define UART_WEKEUP_PIN CM_IOMUX_PIN_17

#define UART_BUF_LEN 1024

/**
 * @brief 通用返回结果
 */
typedef enum
{
    RES_BUSY = -2,  /// 繁忙
    RES_ERROR = -1, /// 失败
    RES_SUCCESS = 0 /// 成功
} COMMON_RES_T;

// 用于AT指令的UART
#define AT_UART_DEV_NUM 0

// 网络指示灯
#define NET_LED_PIN CM_IOMUX_PIN_76
#define NET_LED_GPIO CM_GPIO_NUM_0

// 工作指示灯
#define WORK_LED_PIN CM_IOMUX_PIN_77
#define WORK_LED_GPIO CM_GPIO_NUM_1

// 获取毫秒数
#define get_ms(num) num / 5

#endif