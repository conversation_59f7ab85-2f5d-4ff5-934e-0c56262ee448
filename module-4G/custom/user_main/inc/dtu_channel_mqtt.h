#ifndef __DTU_CHANNEL_MQTT_H__
#define __DTU_CHANNEL_MQTT_H__ 

#include "common.h"
#include "dtu_config.h"
#include "cm_mqtt.h"
#include "dtu_channel.h"

/**
 * @brief mqtt消息结构体
 */
typedef struct
{
    unsigned short msgid;
    char* payload;
    int payload_len;
} mqtt_message_t;

/**
 * @brief mqtt通道上下文结构体
 */
typedef struct
{
    dtu_mqtt_param_t* param;                    // 连接参数
    uint8_t id;                                 // 通道id
    cm_mqtt_client_t* client;                   // mqtt客户端
    cm_mqtt_conn_state_e conn_state;            // 连接状态
    cm_mqtt_client_cb_t* client_callback;       // 客户端回调
    cm_mqtt_connect_options_t* connect_options; // 客户端连接参数
    osMessageQueueId_t read_queue_id;           // 接收队列
    osThreadId_t read_thread_id;                // 接收线程id
    data_receive_callback_t callback;           // 接收回调
    osMessageQueueId_t send_queue_id;           // 发送队列id
    osThreadId_t send_thread_id;                // 发送线程id 
    dynamic_list_t* message_pool;               // 消息池用于存储mqtt分包信息
} mqtt_channel_ctx_t;

/**
 * @brief mqtt通道初始化
 *
 * @param param mqtt参数
 * @param id 通道id
 * @param callback 接收回调
 * @return mqtt_channel_ctx_t* mqtt通道上下文
 */
mqtt_channel_ctx_t* mqtt_channel_init(dtu_mqtt_param_t* param, uint8_t id, data_receive_callback_t callback);

/**
 * @brief mqtt通道发送
 *
 * @param ctx mqtt通道上下文
 * @param data 数据
 * @param len 数据长度
 */
COMMON_RES_T mqtt_channel_send(mqtt_channel_ctx_t* ctx, gc_data_t* data, size_t len);

/**
 * @brief mqtt通道关闭
 *
 * @param ctx mqtt通道上下文
 */
void mqtt_channel_close(mqtt_channel_ctx_t* ctx);

#endif