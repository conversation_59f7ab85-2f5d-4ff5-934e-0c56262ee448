#ifndef __DTU_CHANNEL_UART_H__
#define __DTU_CHANNEL_UART_H__

#include <stdint.h>
#include "common.h"
#include "dtu_config.h"
#include "my_util.h"

/**
 * @brief 串口上下文结构体
 */
typedef struct
{
    dtu_uart_param_t* config;            // 串口配置信息
    uint8_t dev_num;                     // 串口设备编号
    osSemaphoreId_t read_sem;            // 串口读取信号量
    osThreadId_t read_thread_id;         // 读取线程
    osTimerId_t receive_pack_timer_id;   // 接收数据分包定时器
    char* receive_buf;                   // 接收缓存
    uint32_t receive_buf_len;            // 缓存长度
    osTimerId_t auto_pull_thread_id;     // 自动拉取数据线程
    data_receive_callback_t callback;    // 接收回调
    osMessageQueueId_t send_queue_id;    // 发送队列id
    osThreadId_t send_thread_id;         // 发送线程id    
    osMessageQueueId_t consume_queue_id; // 消费队列id
    osThreadId_t consume_thread_id;      // 消费线程id    
} uart_ctx_t;

/**
 * @brief 串口初始化
 * @param config 配置信息
 * @param collect_channel_num 采集通道编号
 * @param callback 接收回调
 * @return 串口上下文，NULL则为初始化失败
 */
uart_ctx_t* uart_channel_init(dtu_uart_param_t* config, uint8_t collect_channel_num, data_receive_callback_t callback);

/**
 * @brief 串口通道发送
 *
 * @param ctx uart通道上下文
 * @param data 数据
 * @param len 数据长度
 */
COMMON_RES_T uart_channel_send(uart_ctx_t* ctx, gc_data_t* data, size_t len);

/**
 * @brief 串口通道关闭
 *
 * @param ctx 串口通道上下文
 */
void uart_channel_close(uart_ctx_t* ctx);

#endif