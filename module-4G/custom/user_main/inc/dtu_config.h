#ifndef __DTU_CONFIG_H__
#define __DTU_CONFIG_H__

#include <stdint.h>
#include "common.h"

// 服务器地址
#define CONFIG_SERVER "https://cn.fenydata.com"
// 获取请求接口
#define CONFIG_SERVER_CONFIG_URI "/config.json"
// 本地保存文件名
#define LOCAL_CONFIG_FILE_NAME "config.json"
// 服务器https证书
#define CONFIG_SERVER_CERTIFICATE \
"-----BEGIN CERTIFICATE-----\r\n" \
"MIIE+TCCA+GgAwIBAgISBZpm4VZR9WWxXHLyydyVdyuCMA0GCSqGSIb3DQEBCwUA\r\n" \
"MDMxCzAJBgNVBAYTAlVTMRYwFAYDVQQKEw1MZXQncyBFbmNyeXB0MQwwCgYDVQQD\r\n" \
"EwNSMTAwHhcNMjUwNjI0MDg0MzAyWhcNMjUwOTIyMDg0MzAxWjAaMRgwFgYDVQQD\r\n" \
"Ew9jbi5mZW55ZGF0YS5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB\r\n" \
"AQCzIRrnx1bjXdy7KXKeYSMkVbn4ex8ysElx54yOCo7uNW/mRKI6JLAEget6LCtx\r\n" \
"lEANFmZ4aGMZwPNSDjJ4gmYNPT/6CBa7IRaziUjSLj7ExFP0atbSP/qb0ug8k18H\r\n" \
"66tU7Z5msWNVKDCdLQfX3wwPrICXkrd115uhzRIG1x3QpEACerxOnw4NVGkEWpYO\r\n" \
"lhdwiXpLJSd5l/mPQo6m6l30m3emOB+p+mZ6gsrAaSrtuAU5dE17NysUKkpC3vdn\r\n" \
"1LaiUMqp2ZGp8jxOHXcrGCIaGA3FXZoOkD0NPz/UziQaavYHqz6a2je/b4vW1nc1\r\n" \
"bqIPADNRpbLpYxSXsMHzVkpBAgMBAAGjggIeMIICGjAOBgNVHQ8BAf8EBAMCBaAw\r\n" \
"HQYDVR0lBBYwFAYIKwYBBQUHAwEGCCsGAQUFBwMCMAwGA1UdEwEB/wQCMAAwHQYD\r\n" \
"VR0OBBYEFOWHlAHMDagVokAV1ol8o9d6D2IGMB8GA1UdIwQYMBaAFLu8w0el5Lyp\r\n" \
"xsOkcgwQjaI14cjoMDMGCCsGAQUFBwEBBCcwJTAjBggrBgEFBQcwAoYXaHR0cDov\r\n" \
"L3IxMC5pLmxlbmNyLm9yZy8wGgYDVR0RBBMwEYIPY24uZmVueWRhdGEuY29tMBMG\r\n" \
"A1UdIAQMMAowCAYGZ4EMAQIBMC0GA1UdHwQmMCQwIqAgoB6GHGh0dHA6Ly9yMTAu\r\n" \
"Yy5sZW5jci5vcmcvMy5jcmwwggEEBgorBgEEAdZ5AgQCBIH1BIHyAPAAdgB9WR4S\r\n" \
"4XgqexxhZ3xe/fjQh1wUoE6VnrkDL9kOjC55uAAAAZehUHC5AAAEAwBHMEUCIQDI\r\n" \
"gEpzJdvQ3wwqoJ2vSFYqYVNKxItLDpjZEhxiRF1G1wIgaseuHOS3AH3iqb8uZiFp\r\n" \
"Ri4uvK8PMBZ4LIQsqAJrekMAdgDd3Mo0ldfhFgXnlTL6x5/4PRxQ39sAOhQSdgos\r\n" \
"rLvIKgAAAZehUICnAAAEAwBHMEUCIDB2gc7m2RUeXD1uNZ9MLUXHM+dFnwceRHUc\r\n" \
"4lcoHW5TAiEAog9jqU9bIOm3re2MMp+xsRZnsfdp4H8kSncp9D+uYaUwDQYJKoZI\r\n" \
"hvcNAQELBQADggEBAIuS8f6s2vU7ljuVRywauSPp0+StcMfGwkJksMofZN99XbPL\r\n" \
"Bdzvm5UQ/NXSTGFLm5MGbEhc427KdrBvC31eoyliMPlqF4a+219EnetmVwqTnTIN\r\n" \
"CNsHFyfI/L0/S/mHut3ZIptgEPVFBwG9mRqqqbiWbsU0pi9SapafeM3X8SVCMTS5\r\n" \
"x6OBSIGAQv4ZMciintKqENME86yM29/9PYFN1Rl0zUW/7K7vmitkkxImWrOQQQsC\r\n" \
"8N6Uk7oakXypNMFr7fYNDbtuCNh3xdIiOtgmJEHiy2OWM8ZylYSLbv3Ly6+vCyy8\r\n" \
"hApeQ5QJ4W9BgVJ3y+CSC2Ri/WKRX+JMEm4OPfM=\r\n" \
"-----END CERTIFICATE-----\r\n" \
"-----BEGIN CERTIFICATE-----\r\n" \
"MIIFBTCCAu2gAwIBAgIQS6hSk/eaL6JzBkuoBI110DANBgkqhkiG9w0BAQsFADBP\r\n" \
"MQswCQYDVQQGEwJVUzEpMCcGA1UEChMgSW50ZXJuZXQgU2VjdXJpdHkgUmVzZWFy\r\n" \
"Y2ggR3JvdXAxFTATBgNVBAMTDElTUkcgUm9vdCBYMTAeFw0yNDAzMTMwMDAwMDBa\r\n" \
"Fw0yNzAzMTIyMzU5NTlaMDMxCzAJBgNVBAYTAlVTMRYwFAYDVQQKEw1MZXQncyBF\r\n" \
"bmNyeXB0MQwwCgYDVQQDEwNSMTAwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK\r\n" \
"AoIBAQDPV+XmxFQS7bRH/sknWHZGUCiMHT6I3wWd1bUYKb3dtVq/+vbOo76vACFL\r\n" \
"YlpaPAEvxVgD9on/jhFD68G14BQHlo9vH9fnuoE5CXVlt8KvGFs3Jijno/QHK20a\r\n" \
"/6tYvJWuQP/py1fEtVt/eA0YYbwX51TGu0mRzW4Y0YCF7qZlNrx06rxQTOr8IfM4\r\n" \
"FpOUurDTazgGzRYSespSdcitdrLCnF2YRVxvYXvGLe48E1KGAdlX5jgc3421H5KR\r\n" \
"mudKHMxFqHJV8LDmowfs/acbZp4/SItxhHFYyTr6717yW0QrPHTnj7JHwQdqzZq3\r\n" \
"DZb3EoEmUVQK7GH29/Xi8orIlQ2NAgMBAAGjgfgwgfUwDgYDVR0PAQH/BAQDAgGG\r\n" \
"MB0GA1UdJQQWMBQGCCsGAQUFBwMCBggrBgEFBQcDATASBgNVHRMBAf8ECDAGAQH/\r\n" \
"AgEAMB0GA1UdDgQWBBS7vMNHpeS8qcbDpHIMEI2iNeHI6DAfBgNVHSMEGDAWgBR5\r\n" \
"tFnme7bl5AFzgAiIyBpY9umbbjAyBggrBgEFBQcBAQQmMCQwIgYIKwYBBQUHMAKG\r\n" \
"Fmh0dHA6Ly94MS5pLmxlbmNyLm9yZy8wEwYDVR0gBAwwCjAIBgZngQwBAgEwJwYD\r\n" \
"VR0fBCAwHjAcoBqgGIYWaHR0cDovL3gxLmMubGVuY3Iub3JnLzANBgkqhkiG9w0B\r\n" \
"AQsFAAOCAgEAkrHnQTfreZ2B5s3iJeE6IOmQRJWjgVzPw139vaBw1bGWKCIL0vIo\r\n" \
"zwzn1OZDjCQiHcFCktEJr59L9MhwTyAWsVrdAfYf+B9haxQnsHKNY67u4s5Lzzfd\r\n" \
"u6PUzeetUK29v+PsPmI2cJkxp+iN3epi4hKu9ZzUPSwMqtCceb7qPVxEbpYxY1p9\r\n" \
"1n5PJKBLBX9eb9LU6l8zSxPWV7bK3lG4XaMJgnT9x3ies7msFtpKK5bDtotij/l0\r\n" \
"GaKeA97pb5uwD9KgWvaFXMIEt8jVTjLEvwRdvCn294GPDF08U8lAkIv7tghluaQh\r\n" \
"1QnlE4SEN4LOECj8dsIGJXpGUk3aU3KkJz9icKy+aUgA+2cP21uh6NcDIS3XyfaZ\r\n" \
"QjmDQ993ChII8SXWupQZVBiIpcWO4RqZk3lr7Bz5MUCwzDIA359e57SSq5CCkY0N\r\n" \
"4B6Vulk7LktfwrdGNVI5BsC9qqxSwSKgRJeZ9wygIaehbHFHFhcBaMDKpiZlBHyz\r\n" \
"rsnnlFXCb5s8HKn5LsUgGvB24L7sGNZP2CX7dhHov+YhD+jozLW2p9W4959Bz2Ei\r\n" \
"RmqDtmiXLnzqTpXbI+suyCsohKRg6Un0RC47+cpiVwHiXZAW+cn8eiNIjqbVgXLx\r\n" \
"KPpdzvvtTnOPlC7SQZSYmdunr3Bf9b77AiC/ZidstK36dRILKz7OA54=\r\n" \
"-----END CERTIFICATE-----"

typedef struct
{
    char* cmd;   // 命令
    uint8_t crc; // 是否添加CRC校验
} dtu_cmd_t;

typedef struct
{
    uint8_t dev_num;          // 串口设备号
    uint8_t enabled;          // 是否使能
    uint8_t byte_size;        // 数据位
    uint8_t parity;           // 校验位 0/1/2
    uint8_t stop_bit;         // 停止位
    uint32_t baudrate;        // 波特率
    uint32_t pack_timeout;    // 数据包超时时间(ms)，两次收到串口数据的时间超过此值，则分两次发送
    uint32_t pack_max_len;    // 数据包最大长度，如果数据长度超过此值，则分多次发送
    char* pack_end_str;       // 数据包结束符，如果串口收到此字符串，则认为数据包结束
    uint64_t pull_interval;   // 主动轮询时间间隔(ms)
    dtu_cmd_t* cmd_list[10];  // 主动轮询命令列表
    uint8_t cmd_cnt;          // 主动轮询命令个数
} dtu_uart_t;

typedef struct
{
    uint8_t dev_num;              // 串口设备号
    uint8_t enabled;              // 是否使能
    char* type;                   // 通道类型 tcp/udp/mqtt/http
    void* param;                  // 通道参数
    char* send_template;          // 发送数据流模板
    char* recv_template;          // 接收数据流模板
} dtu_channel_t;

typedef struct
{
    char* host;                     // 主机地址
    uint16_t port;                  // 端口
    uint8_t ssl_enable;             // 是否启用SSL，UDP不支持SSL
    char* ca_cert;                  // SSL证书
    uint16_t heart_interval;        // 心跳间隔(ms)
    char* heart_content_template;   // 心跳内容模板
    uint8_t heart_content_hex_mode; // 心跳内容是否为16进制
} dtu_socket_param_t;

typedef struct
{
    char* host;               // 主机地址
    uint16_t port;            // 端口
    char* client_id;          // 客户端ID，不设置则用IMEI做为客户端ID
    char* username;           // 账号
    char* password;           // 密码
    uint8_t clean_session;    // 是否清除会话
    uint16_t heart_interval;  // 心跳包的间隔(ms)
    char* sub_topic;          // 订阅主题
    char* pub_topic;          // 发布主题
    uint8_t qos;              // QOS等级 0/1/2
    uint8_t pub_retain;       // 发布消息是否保留 0/1
    uint8_t last_will_enable; // 遗嘱消息 0/1
    char* last_will_topic;    // 遗嘱消息主题
    uint8_t last_will_qos;    // 遗嘱消息QOS等级 0/1/2
    uint8_t last_will_retain; // 遗嘱消息是否保留 0/1
    char* last_will_content;  // 遗嘱消息内容
    uint8_t ssl_enable;       // 是否启用SSL
    char* ca_cert;            // SSL证书
} dtu_mqtt_param_t;

typedef struct
{
    char* server;         // 服务器地址
    char* path;           // 请求的路径
    char* method;         // 请求方式 GET/POST
    uint16_t timeout;     // 请求超时时间(ms)
    char* request_type;   // 请求类型(串口数据参数位置) inBody/inUrl
    char* content_type;   // body的数据类型 application/x-www-form-urlencoded application/json application/octet-stream
    char* header;         // 自定义header
    uint8_t ssl_enable;   // 是否启用SSL
    char* ca_cert;        // SSL证书
} dtu_http_param_t;

typedef struct
{
    char* dtu_code;                  // 设备编码
    char* version;                   // 版本号
    dtu_uart_t* uart_list[3];        // 串口配置列表
    int uart_cnt;                    // 串口配置数量
    dtu_channel_t* channel_list[5];  // 通道配置列表
    int channel_cnt;                 // 通道配置数量
} dtu_config_t;

/**
 * @brief 获取DTU配置
 *
 * @param [out] config DTU配置结构体指针
 *
 * @return 获取结果
 * @retval RET_SUCCESS 获取成功
 * @retval RET_ERROR 获取失败
 */
COMMON_RES_T get_dtu_config(dtu_config_t* config);

/**
 * @brief 解析配置
 *
 * @param config_json_str 配置内容
 * @param config 配置结构体指针
 * @return COMMON_RES_T 解析结果
 */
COMMON_RES_T parse_dtu_config(char* config_json_str, dtu_config_t* config);

#endif