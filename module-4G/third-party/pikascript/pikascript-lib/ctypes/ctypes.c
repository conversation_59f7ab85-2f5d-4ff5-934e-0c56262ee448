#include "ctypes_c_buffer.h"
#include "ctypes_c_float.h"
#include "ctypes_c_uint.h"
#include "ctypes_c_wchar_p.h"
#include "ctypes_utils.h"

void ctypes_c_uint___init__(PikaObj* self, int value) {
    ctypesUtils_setInt(self, value);
}

void ctypes_c_wchar_p___init__(PikaObj* self, char* value) {
    ctypesUtils_setStr(self, value);
}

void ctypes_c_float___init__(PikaObj* self, pika_float value) {
    ctypesUtils_setFloat(self, value);
}

void ctypes_create_string_buffer___init__(PikaObj* self, int size) {
    uint8_t* buffer;
    obj_setBytes(self, "raw", NULL, size);
    buffer = obj_getBytes(self, "raw");
    __platform_printf("0x%lx", (uintptr_t)&buffer);
}

int ctypes_create_string_buffer___getitem__(PikaObj* self, int __key) {
    uint8_t* buffer;
    int i;

    i = __key;
    buffer = obj_getBytes(self, "raw");
    return buffer[i];
}

int ctypes_c_buffer___getitem__(PikaObj* self, int __key) {
    int i;
    uint8_t* buffer;

    i = __key;
    buffer = obj_getBytes(self, "raw");
    return buffer[i];
}

void ctypes_c_buffer___init__(PikaObj* self, Arg* value, int size) {
    uint8_t* buffer;
    uint8_t* value_buffer;
    size_t value_size;
    ArgType arg_type;

    arg_type = arg_getType(value);
    if (arg_type == ARG_TYPE_BYTES) {
        obj_setBytes(self, "raw", NULL, size);
        buffer = obj_getBytes(self, "raw");
        value_size = arg_getBytesSize(value);
        value_buffer = arg_getBytes(value);
        __platform_memcpy(buffer, value_buffer, value_size);
    } else if (ARG_TYPE_STRING == arg_type) {
        obj_setBytes(self, "raw", NULL, size);
        buffer = obj_getBytes(self, "raw");
        value_buffer = (uint8_t*)arg_getStr(value);
        __platform_memcpy(buffer, value_buffer,
                          strGetSize((char*)value_buffer) + 1);
    } else {
        __platform_printf("value type is not support!");
        __platform_panic_handle();
    }
}
