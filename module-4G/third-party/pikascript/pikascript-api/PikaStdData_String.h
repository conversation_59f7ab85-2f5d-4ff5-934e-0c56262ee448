/*
 * [Warning!] This file is auto-generated by pika compiler.
 * Do not edit it manually.
 * The source code is *.pyi file.
 * More details: 
 * English Doc:
 * https://pikadoc-en.readthedocs.io/en/latest/PikaScript%20%E6%A8%A1%E5%9D%97%E6%A6%82%E8%BF%B0.html
 * Chinese Doc:
 * http://pikapython.com/doc/PikaScript%20%E6%A8%A1%E5%9D%97%E6%A6%82%E8%BF%B0.html
 */

#ifndef __PikaStdData_String__H
#define __PikaStdData_String__H
#include <stdio.h>
#include <stdlib.h>
#include "PikaObj.h"

PikaObj *New_PikaStdData_String(Args *args);

Arg* PikaStdData_String___getitem__(PikaObj *self, Arg* __key);
void PikaStdData_String___init__(PikaObj *self, char* s);
Arg* PikaStdData_String___iter__(PikaObj *self);
int PikaStdData_String___len__(PikaObj *self);
Arg* PikaStdData_String___next__(PikaObj *self);
void PikaStdData_String___setitem__(PikaObj *self, Arg* __key, Arg* __val);
char* PikaStdData_String___str__(PikaObj *self);
Arg* PikaStdData_String_encode(PikaObj *self, PikaTuple* encoding);
int PikaStdData_String_endswith(PikaObj *self, char* suffix);
int PikaStdData_String_find(PikaObj *self, char* sub);
char* PikaStdData_String_format(PikaObj *self, PikaTuple* vars);
char* PikaStdData_String_get(PikaObj *self);
int PikaStdData_String_isalnum(PikaObj *self);
int PikaStdData_String_isalpha(PikaObj *self);
int PikaStdData_String_isdigit(PikaObj *self);
int PikaStdData_String_islower(PikaObj *self);
int PikaStdData_String_isspace(PikaObj *self);
char* PikaStdData_String_join(PikaObj *self, Arg* val);
char* PikaStdData_String_replace(PikaObj *self, char* old, char* new);
void PikaStdData_String_set(PikaObj *self, char* s);
PikaObj* PikaStdData_String_split(PikaObj *self, PikaTuple* s);
int PikaStdData_String_startswith(PikaObj *self, char* prefix);
char* PikaStdData_String_strip(PikaObj *self, PikaTuple* chrs);

#endif
