/*
 * [Warning!] This file is auto-generated by pika compiler.
 * Do not edit it manually.
 * The source code is *.pyi file.
 * More details: 
 * English Doc:
 * https://pikadoc-en.readthedocs.io/en/latest/PikaScript%20%E6%A8%A1%E5%9D%97%E6%A6%82%E8%BF%B0.html
 * Chinese Doc:
 * http://pikapython.com/doc/PikaScript%20%E6%A8%A1%E5%9D%97%E6%A6%82%E8%BF%B0.html
 */

#ifndef __PikaStdData_List__H
#define __PikaStdData_List__H
#include <stdio.h>
#include <stdlib.h>
#include "PikaObj.h"

PikaObj *New_PikaStdData_List(Args *args);

PikaObj* PikaStdData_List___add__(PikaObj *self, PikaObj* others);
void PikaStdData_List___init__(PikaObj *self);
PikaObj* PikaStdData_List___mul__(PikaObj *self, int n);
void PikaStdData_List___setitem__(PikaObj *self, Arg* __key, Arg* __val);
char* PikaStdData_List___str__(PikaObj *self);
void PikaStdData_List_append(PikaObj *self, Arg* arg);
void PikaStdData_List_insert(PikaObj *self, int i, Arg* arg);
Arg* PikaStdData_List_pop(PikaObj *self, PikaTuple* index);
void PikaStdData_List_remove(PikaObj *self, Arg* val);
void PikaStdData_List_reverse(PikaObj *self);
void PikaStdData_List_set(PikaObj *self, int i, Arg* arg);

#endif
