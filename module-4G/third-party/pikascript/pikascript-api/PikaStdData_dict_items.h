/*
 * [Warning!] This file is auto-generated by pika compiler.
 * Do not edit it manually.
 * The source code is *.pyi file.
 * More details: 
 * English Doc:
 * https://pikadoc-en.readthedocs.io/en/latest/PikaScript%20%E6%A8%A1%E5%9D%97%E6%A6%82%E8%BF%B0.html
 * Chinese Doc:
 * http://pikapython.com/doc/PikaScript%20%E6%A8%A1%E5%9D%97%E6%A6%82%E8%BF%B0.html
 */

#ifndef __PikaStdData_dict_items__H
#define __PikaStdData_dict_items__H
#include <stdio.h>
#include <stdlib.h>
#include "PikaObj.h"

PikaObj *New_PikaStdData_dict_items(Args *args);

Arg* PikaStdData_dict_items___iter__(PikaObj *self);
int PikaStdData_dict_items___len__(PikaObj *self);
Arg* PikaStdData_dict_items___next__(PikaObj *self);
char* PikaStdData_dict_items___str__(PikaObj *self);

#endif
