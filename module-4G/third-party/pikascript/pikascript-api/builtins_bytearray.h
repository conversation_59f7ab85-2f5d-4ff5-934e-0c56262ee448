/*
 * [Warning!] This file is auto-generated by pika compiler.
 * Do not edit it manually.
 * The source code is *.pyi file.
 * More details: 
 * English Doc:
 * https://pikadoc-en.readthedocs.io/en/latest/PikaScript%20%E6%A8%A1%E5%9D%97%E6%A6%82%E8%BF%B0.html
 * Chinese Doc:
 * http://pikapython.com/doc/PikaScript%20%E6%A8%A1%E5%9D%97%E6%A6%82%E8%BF%B0.html
 */

#ifndef __builtins_bytearray__H
#define __builtins_bytearray__H
#include <stdio.h>
#include <stdlib.h>
#include "PikaObj.h"

PikaObj *New_builtins_bytearray(Args *args);

int builtins_bytearray___contains__(PikaObj *self, Arg* others);
int builtins_bytearray___getitem__(PikaObj *self, int __key);
void builtins_bytearray___init__(PikaObj *self, Arg* bytes);
Arg* builtins_bytearray___iter__(PikaObj *self);
int builtins_bytearray___len__(PikaObj *self);
Arg* builtins_bytearray___next__(PikaObj *self);
void builtins_bytearray___setitem__(PikaObj *self, int __key, int __val);
char* builtins_bytearray___str__(PikaObj *self);
char* builtins_bytearray_decode(PikaObj *self);
PikaObj* builtins_bytearray_split(PikaObj *self, PikaTuple* vars);

#endif
