#include "PikaPlatform.h"
/* warning: auto generated file, please do not modify */
PIKA_BYTECODE_ALIGN const unsigned char pikaModules_py_a[] = {
    0x0f, 0x70, 0x79, 0x61, 0x04, 0x01, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 
    0x01, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x6a, 0x73, 0x6f, 0x6e, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x34, 0x00, 0x00, 0x00, 
    0x00, 0x96, 0x01, 0x00, 0x00, 0x89, 0x07, 0x00, 0x00, 0x06, 0x17, 0x00, 
    0x11, 0x81, 0x19, 0x00, 0x01, 0x02, 0x1e, 0x00, 0x01, 0x0a, 0x00, 0x00, 
    0x01, 0x8a, 0x00, 0x00, 0x00, 0x89, 0x2a, 0x00, 0x00, 0x06, 0x17, 0x00, 
    0x11, 0x81, 0x38, 0x00, 0x01, 0x02, 0x3a, 0x00, 0x01, 0x0a, 0x00, 0x00, 
    0x01, 0x8a, 0x00, 0x00, 0x46, 0x00, 0x00, 0x00, 0x00, 0x5f, 0x6a, 0x73, 
    0x6f, 0x6e, 0x00, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x28, 0x6a, 0x73, 0x6f, 
    0x6e, 0x3a, 0x73, 0x74, 0x72, 0x29, 0x00, 0x31, 0x00, 0x6a, 0x73, 0x6f, 
    0x6e, 0x00, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x2e, 0x6c, 0x6f, 0x61, 0x64, 
    0x73, 0x00, 0x64, 0x75, 0x6d, 0x70, 0x73, 0x28, 0x64, 0x3a, 0x64, 0x69, 
    0x63, 0x74, 0x29, 0x00, 0x64, 0x00, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x2e, 
    0x64, 0x75, 0x6d, 0x70, 0x73, 0x00, 0x00, 0x00, 
};
