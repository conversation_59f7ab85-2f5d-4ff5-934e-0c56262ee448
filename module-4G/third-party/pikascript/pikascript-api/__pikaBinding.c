/*
 * [Warning!] This file is auto-generated by pika compiler.
 * Do not edit it manually.
 * The source code is *.pyi file.
 * More details: 
 * English Doc:
 * https://pikadoc-en.readthedocs.io/en/latest/PikaScript%20%E6%A8%A1%E5%9D%97%E6%A6%82%E8%BF%B0.html
 * Chinese Doc:
 * http://pikapython.com/doc/PikaScript%20%E6%A8%A1%E5%9D%97%E6%A6%82%E8%BF%B0.html
 */

#include <stdio.h>
#include <stdlib.h>
#include "BaseObj.h"
#include "PikaDebug.h"
#include "TinyObj.h"

#include "PikaDebug_Debuger.h"
#include "PikaMain.h"
#include "PikaStdLib_SysObj.h"
#include "PikaStdData.h"
#include "PikaStdLib.h"
#include "_json.h"
#include "builtins.h"
#include "PikaStdData_ByteArray.h"
#include "builtins_bytearray.h"
#include "PikaStdData_Dict.h"
#include "PikaStdData_FILEIO.h"
#include "PikaStdData_List.h"
#include "PikaStdData_Tuple.h"
#include "PikaStdData_String.h"
#include "PikaStdData_Utils.h"
#include "PikaStdData_dict_items.h"
#include "PikaStdData_dict_keys.h"
#include "PikaStdLib_MemChecker.h"
#include "PikaStdLib_REPL.h"
#include "PikaStdTask.h"
#include "PikaStdTask_Task.h"
#include "builtins_ArithmeticError.h"
#include "builtins_Exception.h"
#include "builtins_AssertionError.h"
#include "builtins_AttributeError.h"
#include "builtins_BaseException.h"
#include "builtins_BlockingIOError.h"
#include "builtins_OSError.h"
#include "builtins_BrokenPipeError.h"
#include "builtins_ConnectionError.h"
#include "builtins_BufferError.h"
#include "builtins_BytesWarning.h"
#include "builtins_Warning.h"
#include "builtins_ChildProcessError.h"
#include "builtins_ConnectionAbortedError.h"
#include "builtins_ConnectionRefusedError.h"
#include "builtins_ConnectionResetError.h"
#include "builtins_DeprecationWarning.h"
#include "builtins_EOFError.h"
#include "builtins_FileExistsError.h"
#include "builtins_FileNotFoundError.h"
#include "builtins_FloatingPointError.h"
#include "builtins_FutureWarning.h"
#include "builtins_GeneratorExit.h"
#include "builtins_ImportError.h"
#include "builtins_ImportWarning.h"
#include "builtins_IndentationError.h"
#include "builtins_SyntaxError.h"
#include "builtins_IndexError.h"
#include "builtins_LookupError.h"
#include "builtins_InterruptedError.h"
#include "builtins_IsADirectoryError.h"
#include "builtins_KeyError.h"
#include "builtins_KeyboardInterrupt.h"
#include "builtins_MemoryError.h"
#include "builtins_ModuleNotFoundError.h"
#include "builtins_NameError.h"
#include "builtins_NotADirectoryError.h"
#include "builtins_NotImplementedError.h"
#include "builtins_RuntimeError.h"
#include "builtins_OverflowError.h"
#include "builtins_PendingDeprecationWarning.h"
#include "builtins_PermissionError.h"
#include "builtins_ProcessLookupError.h"
#include "builtins_RangeObj.h"
#include "builtins_RecursionError.h"
#include "builtins_ReferenceError.h"
#include "builtins_ResourceWarning.h"
#include "builtins_RuntimeWarning.h"
#include "builtins_StopAsyncIteration.h"
#include "builtins_StopIteration.h"
#include "builtins_StringObj.h"
#include "builtins_SyntaxWarning.h"
#include "builtins_SystemError.h"
#include "builtins_SystemExit.h"
#include "builtins_TabError.h"
#include "builtins_TimeoutError.h"
#include "builtins_TypeError.h"
#include "builtins_UnboundLocalError.h"
#include "builtins_UnicodeDecodeError.h"
#include "builtins_UnicodeError.h"
#include "builtins_UnicodeEncodeError.h"
#include "builtins_ValueError.h"
#include "builtins_UnicodeTranslateError.h"
#include "builtins_UnicodeWarning.h"
#include "builtins_UserWarning.h"
#include "builtins_ZeroDivisionError.h"
#include "builtins_object.h"
#include "ctypes.h"
#include "ctypes_c_bool.h"
#include "ctypes_c_uint.h"
#include "ctypes_c_buffer.h"
#include "ctypes_c_byte.h"
#include "ctypes_c_char.h"
#include "ctypes_c_wchar_p.h"
#include "ctypes_c_char_p.h"
#include "ctypes_c_double.h"
#include "ctypes_c_float.h"
#include "ctypes_c_int.h"
#include "ctypes_c_long.h"
#include "ctypes_c_longdouble.h"
#include "ctypes_c_longlong.h"
#include "ctypes_c_short.h"
#include "ctypes_c_size_t.h"
#include "ctypes_c_ssize_t.h"
#include "ctypes_c_ubyte.h"
#include "ctypes_c_ulong.h"
#include "ctypes_c_ulonglong.h"
#include "ctypes_c_void_p.h"
#include "ctypes_c_wchar.h"
#include "ctypes_create_string_buffer.h"

#ifndef PIKA_MODULE_PIKADEBUG_DISABLE
void PikaDebug_DebugerMethod(PikaObj *self, Args *_args_){
    Arg* res = PikaDebug_Debuger(self);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaDebug_Debuger,
    "Debuger", ""
);

void PikaDebug_reset_breakMethod(PikaObj *self, Args *_args_){
    char* module = args_getStr(_args_, "module");
    int pc_break = args_getInt(_args_, "pc_break");
    PikaDebug_reset_break(self, module, pc_break);
}
method_typedef(
    PikaDebug_reset_break,
    "reset_break", "module,pc_break"
);

void PikaDebug_set_breakMethod(PikaObj *self, Args *_args_){
    char* module = args_getStr(_args_, "module");
    int pc_break = args_getInt(_args_, "pc_break");
    PikaDebug_set_break(self, module, pc_break);
}
method_typedef(
    PikaDebug_set_break,
    "set_break", "module,pc_break"
);

void PikaDebug_set_traceMethod(PikaObj *self, Args *_args_){
    PikaDebug_set_trace(self);
}
method_typedef(
    PikaDebug_set_trace,
    "set_trace", ""
);

class_def(PikaDebug){
    __BEFORE_MOETHOD_DEF
    method_def(PikaDebug_reset_break, 921221996),
    method_def(PikaDebug_set_break, 1109886261),
    method_def(PikaDebug_set_trace, 1131228543),
    constructor_def(PikaDebug_Debuger, 1761613187),
};
class_inhert(PikaDebug, TinyObj);

PikaObj *New_PikaDebug(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, PikaDebug);
    return self;
}
#endif

#ifndef PIKA_MODULE_PIKADEBUG_DISABLE
void PikaDebug_Debuger___init__Method(PikaObj *self, Args *_args_){
    PikaDebug_Debuger___init__(self);
}
method_typedef(
    PikaDebug_Debuger___init__,
    "__init__", ""
);

void PikaDebug_Debuger_set_traceMethod(PikaObj *self, Args *_args_){
    PikaDebug_Debuger_set_trace(self);
}
method_typedef(
    PikaDebug_Debuger_set_trace,
    "set_trace", ""
);

class_def(PikaDebug_Debuger){
    __BEFORE_MOETHOD_DEF
    method_def(PikaDebug_Debuger___init__, 904762485),
    method_def(PikaDebug_Debuger_set_trace, 1131228543),
};
class_inhert(PikaDebug_Debuger, TinyObj);

PikaObj *New_PikaDebug_Debuger(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, PikaDebug_Debuger);
    return self;
}

Arg *PikaDebug_Debuger(PikaObj *self){
    return obj_newObjInPackage(New_PikaDebug_Debuger);
}
#endif

#ifndef PIKA_MODULE_MAIN_DISABLE
class_def(PikaMain){
    __BEFORE_MOETHOD_DEF
};
class_inhert(PikaMain, PikaStdLib_SysObj);

PikaObj *New_PikaMain(Args *args){
    PikaObj *self = New_PikaStdLib_SysObj(args);
#ifndef PIKA_MODULE_PIKADEBUG_DISABLE
    obj_newObj(self, "PikaDebug", "PikaDebug", New_PikaDebug);
#endif
#ifndef PIKA_MODULE_PIKASTDDATA_DISABLE
    obj_newObj(self, "PikaStdData", "PikaStdData", New_PikaStdData);
#endif
#ifndef PIKA_MODULE_PIKASTDLIB_DISABLE
    obj_newObj(self, "PikaStdLib", "PikaStdLib", New_PikaStdLib);
#endif
#ifndef PIKA_MODULE__JSON_DISABLE
    obj_newObj(self, "_json", "_json", New__json);
#endif
#ifndef PIKA_MODULE_BUILTINS_DISABLE
    obj_newObj(self, "builtins", "builtins", New_builtins);
#endif
    obj_setClass(self, PikaMain);
    return self;
}

Arg *PikaMain(PikaObj *self){
    return obj_newObjInPackage(New_PikaMain);
}
#endif

#ifndef PIKA_MODULE_PIKASTDDATA_DISABLE
void PikaStdData_ByteArrayMethod(PikaObj *self, Args *_args_){
    Arg* res = PikaStdData_ByteArray(self);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_ByteArray,
    "ByteArray", ""
);

void PikaStdData_DictMethod(PikaObj *self, Args *_args_){
    Arg* res = PikaStdData_Dict(self);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_Dict,
    "Dict", ""
);

void PikaStdData_FILEIOMethod(PikaObj *self, Args *_args_){
    Arg* res = PikaStdData_FILEIO(self);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_FILEIO,
    "FILEIO", ""
);

void PikaStdData_ListMethod(PikaObj *self, Args *_args_){
    Arg* res = PikaStdData_List(self);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_List,
    "List", ""
);

void PikaStdData_StringMethod(PikaObj *self, Args *_args_){
    Arg* res = PikaStdData_String(self);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_String,
    "String", ""
);

void PikaStdData_TupleMethod(PikaObj *self, Args *_args_){
    Arg* res = PikaStdData_Tuple(self);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_Tuple,
    "Tuple", ""
);

void PikaStdData_UtilsMethod(PikaObj *self, Args *_args_){
    Arg* res = PikaStdData_Utils(self);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_Utils,
    "Utils", ""
);

void PikaStdData_dict_itemsMethod(PikaObj *self, Args *_args_){
    Arg* res = PikaStdData_dict_items(self);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_dict_items,
    "dict_items", ""
);

void PikaStdData_dict_keysMethod(PikaObj *self, Args *_args_){
    Arg* res = PikaStdData_dict_keys(self);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_dict_keys,
    "dict_keys", ""
);

class_def(PikaStdData){
    __BEFORE_MOETHOD_DEF
    constructor_def(PikaStdData_Tuple, 238099855),
    constructor_def(PikaStdData_Utils, 239242230),
    constructor_def(PikaStdData_FILEIO, 813431197),
    constructor_def(PikaStdData_dict_keys, 872966404),
    constructor_def(PikaStdData_dict_items, 888749258),
    constructor_def(PikaStdData_String, 1374591964),
    constructor_def(PikaStdData_ByteArray, 1998882840),
    constructor_def(PikaStdData_Dict, 2089035049),
    constructor_def(PikaStdData_List, 2089323073),
};
class_inhert(PikaStdData, TinyObj);

PikaObj *New_PikaStdData(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, PikaStdData);
    return self;
}
#endif

#ifndef PIKA_MODULE_PIKASTDDATA_DISABLE
class_def(PikaStdData_ByteArray){
    __BEFORE_MOETHOD_DEF
};
class_inhert(PikaStdData_ByteArray, builtins_bytearray);

PikaObj *New_PikaStdData_ByteArray(Args *args){
    PikaObj *self = New_builtins_bytearray(args);
    obj_setClass(self, PikaStdData_ByteArray);
    return self;
}

Arg *PikaStdData_ByteArray(PikaObj *self){
    return obj_newObjInPackage(New_PikaStdData_ByteArray);
}
#endif

#ifndef PIKA_MODULE_PIKASTDDATA_DISABLE
void PikaStdData_Dict___contains__Method(PikaObj *self, Args *_args_){
    Arg* val = args_getArg(_args_, "val");
    int res = PikaStdData_Dict___contains__(self, val);
    method_returnInt(_args_, res);
}
method_typedef(
    PikaStdData_Dict___contains__,
    "__contains__", "val"
);

void PikaStdData_Dict___del__Method(PikaObj *self, Args *_args_){
    PikaStdData_Dict___del__(self);
}
method_typedef(
    PikaStdData_Dict___del__,
    "__del__", ""
);

void PikaStdData_Dict___eq__Method(PikaObj *self, Args *_args_){
    Arg* other = args_getArg(_args_, "other");
    int res = PikaStdData_Dict___eq__(self, other);
    method_returnInt(_args_, res);
}
method_typedef(
    PikaStdData_Dict___eq__,
    "__eq__", "other"
);

void PikaStdData_Dict___getitem__Method(PikaObj *self, Args *_args_){
    Arg* __key = args_getArg(_args_, "__key");
    Arg* res = PikaStdData_Dict___getitem__(self, __key);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_Dict___getitem__,
    "__getitem__", "__key"
);

void PikaStdData_Dict___init__Method(PikaObj *self, Args *_args_){
    PikaStdData_Dict___init__(self);
}
method_typedef(
    PikaStdData_Dict___init__,
    "__init__", ""
);

void PikaStdData_Dict___iter__Method(PikaObj *self, Args *_args_){
    Arg* res = PikaStdData_Dict___iter__(self);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_Dict___iter__,
    "__iter__", ""
);

void PikaStdData_Dict___len__Method(PikaObj *self, Args *_args_){
    int res = PikaStdData_Dict___len__(self);
    method_returnInt(_args_, res);
}
method_typedef(
    PikaStdData_Dict___len__,
    "__len__", ""
);

void PikaStdData_Dict___next__Method(PikaObj *self, Args *_args_){
    Arg* res = PikaStdData_Dict___next__(self);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_Dict___next__,
    "__next__", ""
);

void PikaStdData_Dict___setitem__Method(PikaObj *self, Args *_args_){
    Arg* __key = args_getArg(_args_, "__key");
    Arg* __val = args_getArg(_args_, "__val");
    PikaStdData_Dict___setitem__(self, __key, __val);
}
method_typedef(
    PikaStdData_Dict___setitem__,
    "__setitem__", "__key,__val"
);

void PikaStdData_Dict___str__Method(PikaObj *self, Args *_args_){
    char* res = PikaStdData_Dict___str__(self);
    method_returnStr(_args_, res);
}
method_typedef(
    PikaStdData_Dict___str__,
    "__str__", ""
);

void PikaStdData_Dict_getMethod(PikaObj *self, Args *_args_){
    char* key = args_getStr(_args_, "key");
    Arg* res = PikaStdData_Dict_get(self, key);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_Dict_get,
    "get", "key"
);

void PikaStdData_Dict_itemsMethod(PikaObj *self, Args *_args_){
    PikaObj* res = PikaStdData_Dict_items(self);
    method_returnObj(_args_, res);
}
method_typedef(
    PikaStdData_Dict_items,
    "items", ""
);

void PikaStdData_Dict_keysMethod(PikaObj *self, Args *_args_){
    PikaObj* res = PikaStdData_Dict_keys(self);
    method_returnObj(_args_, res);
}
method_typedef(
    PikaStdData_Dict_keys,
    "keys", ""
);

void PikaStdData_Dict_removeMethod(PikaObj *self, Args *_args_){
    char* key = args_getStr(_args_, "key");
    PikaStdData_Dict_remove(self, key);
}
method_typedef(
    PikaStdData_Dict_remove,
    "remove", "key"
);

void PikaStdData_Dict_setMethod(PikaObj *self, Args *_args_){
    char* key = args_getStr(_args_, "key");
    Arg* arg = args_getArg(_args_, "arg");
    PikaStdData_Dict_set(self, key, arg);
}
method_typedef(
    PikaStdData_Dict_set,
    "set", "key,arg"
);

void PikaStdData_Dict_updateMethod(PikaObj *self, Args *_args_){
    PikaObj* other = args_getPtr(_args_, "other");
    PikaStdData_Dict_update(self, other);
}
method_typedef(
    PikaStdData_Dict_update,
    "update", "other"
);

class_def(PikaStdData_Dict){
    __BEFORE_MOETHOD_DEF
    method_def(PikaStdData_Dict_get, 193492613),
    method_def(PikaStdData_Dict_set, 193505681),
    method_def(PikaStdData_Dict_items, 262956327),
    method_def(PikaStdData_Dict_remove, 422343795),
    method_def(PikaStdData_Dict_update, 552456360),
    method_def(PikaStdData_Dict___init__, 904762485),
    method_def(PikaStdData_Dict___iter__, 911732085),
    method_def(PikaStdData_Dict___next__, 1090305216),
    method_def(PikaStdData_Dict___setitem__, **********),
    method_def(PikaStdData_Dict___getitem__, 1535436016),
    method_def(PikaStdData_Dict___contains__, 1644201824),
    method_def(PikaStdData_Dict___eq__, 1818853367),
    method_def(PikaStdData_Dict___del__, 2038499702),
    method_def(PikaStdData_Dict___len__, 2047989248),
    method_def(PikaStdData_Dict___str__, 2056834106),
    method_def(PikaStdData_Dict_keys, 2090432961),
};
class_inhert(PikaStdData_Dict, TinyObj);

PikaObj *New_PikaStdData_Dict(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, PikaStdData_Dict);
    return self;
}

Arg *PikaStdData_Dict(PikaObj *self){
    return obj_newObjInPackage(New_PikaStdData_Dict);
}
#endif

#ifndef PIKA_MODULE_PIKASTDDATA_DISABLE
void PikaStdData_FILEIO_closeMethod(PikaObj *self, Args *_args_){
    PikaStdData_FILEIO_close(self);
}
method_typedef(
    PikaStdData_FILEIO_close,
    "close", ""
);

void PikaStdData_FILEIO_initMethod(PikaObj *self, Args *_args_){
    char* path = args_getStr(_args_, "path");
    char* mode = args_getStr(_args_, "mode");
    int res = PikaStdData_FILEIO_init(self, path, mode);
    method_returnInt(_args_, res);
}
method_typedef(
    PikaStdData_FILEIO_init,
    "init", "path,mode"
);

void PikaStdData_FILEIO_readMethod(PikaObj *self, Args *_args_){
    PikaTuple* size = args_getTuple(_args_, "size");
    Arg* res = PikaStdData_FILEIO_read(self, size);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_FILEIO_read,
    "read", "*size"
);

void PikaStdData_FILEIO_readlineMethod(PikaObj *self, Args *_args_){
    char* res = PikaStdData_FILEIO_readline(self);
    method_returnStr(_args_, res);
}
method_typedef(
    PikaStdData_FILEIO_readline,
    "readline", ""
);

void PikaStdData_FILEIO_readlinesMethod(PikaObj *self, Args *_args_){
    PikaObj* res = PikaStdData_FILEIO_readlines(self);
    method_returnObj(_args_, res);
}
method_typedef(
    PikaStdData_FILEIO_readlines,
    "readlines", ""
);

void PikaStdData_FILEIO_seekMethod(PikaObj *self, Args *_args_){
    int offset = args_getInt(_args_, "offset");
    PikaTuple* fromwhere = args_getTuple(_args_, "fromwhere");
    int res = PikaStdData_FILEIO_seek(self, offset, fromwhere);
    method_returnInt(_args_, res);
}
method_typedef(
    PikaStdData_FILEIO_seek,
    "seek", "offset,*fromwhere"
);

void PikaStdData_FILEIO_tellMethod(PikaObj *self, Args *_args_){
    int res = PikaStdData_FILEIO_tell(self);
    method_returnInt(_args_, res);
}
method_typedef(
    PikaStdData_FILEIO_tell,
    "tell", ""
);

void PikaStdData_FILEIO_writeMethod(PikaObj *self, Args *_args_){
    Arg* s = args_getArg(_args_, "s");
    int res = PikaStdData_FILEIO_write(self, s);
    method_returnInt(_args_, res);
}
method_typedef(
    PikaStdData_FILEIO_write,
    "write", "s"
);

void PikaStdData_FILEIO_writelinesMethod(PikaObj *self, Args *_args_){
    PikaObj* lines = args_getPtr(_args_, "lines");
    PikaStdData_FILEIO_writelines(self, lines);
}
method_typedef(
    PikaStdData_FILEIO_writelines,
    "writelines", "lines"
);

class_def(PikaStdData_FILEIO){
    __BEFORE_MOETHOD_DEF
    method_def(PikaStdData_FILEIO_close, 255564379),
    method_def(PikaStdData_FILEIO_write, 279491920),
    method_def(PikaStdData_FILEIO_readlines, 594708860),
    method_def(PikaStdData_FILEIO_writelines, 836522731),
    method_def(PikaStdData_FILEIO_readline, 2035354601),
    method_def(PikaStdData_FILEIO_init, 2090370361),
    method_def(PikaStdData_FILEIO_read, 2090683713),
    method_def(PikaStdData_FILEIO_seek, 2090719789),
    method_def(PikaStdData_FILEIO_tell, 2090755958),
};
class_inhert(PikaStdData_FILEIO, TinyObj);

PikaObj *New_PikaStdData_FILEIO(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, PikaStdData_FILEIO);
    return self;
}

Arg *PikaStdData_FILEIO(PikaObj *self){
    return obj_newObjInPackage(New_PikaStdData_FILEIO);
}
#endif

#ifndef PIKA_MODULE_PIKASTDDATA_DISABLE
void PikaStdData_List___add__Method(PikaObj *self, Args *_args_){
    PikaObj* others = args_getPtr(_args_, "others");
    PikaObj* res = PikaStdData_List___add__(self, others);
    method_returnObj(_args_, res);
}
method_typedef(
    PikaStdData_List___add__,
    "__add__", "others"
);

void PikaStdData_List___init__Method(PikaObj *self, Args *_args_){
    PikaStdData_List___init__(self);
}
method_typedef(
    PikaStdData_List___init__,
    "__init__", ""
);

void PikaStdData_List___mul__Method(PikaObj *self, Args *_args_){
    int n = args_getInt(_args_, "n");
    PikaObj* res = PikaStdData_List___mul__(self, n);
    method_returnObj(_args_, res);
}
method_typedef(
    PikaStdData_List___mul__,
    "__mul__", "n"
);

void PikaStdData_List___setitem__Method(PikaObj *self, Args *_args_){
    Arg* __key = args_getArg(_args_, "__key");
    Arg* __val = args_getArg(_args_, "__val");
    PikaStdData_List___setitem__(self, __key, __val);
}
method_typedef(
    PikaStdData_List___setitem__,
    "__setitem__", "__key,__val"
);

void PikaStdData_List___str__Method(PikaObj *self, Args *_args_){
    char* res = PikaStdData_List___str__(self);
    method_returnStr(_args_, res);
}
method_typedef(
    PikaStdData_List___str__,
    "__str__", ""
);

void PikaStdData_List_appendMethod(PikaObj *self, Args *_args_){
    Arg* arg = args_getArg(_args_, "arg");
    PikaStdData_List_append(self, arg);
}
method_typedef(
    PikaStdData_List_append,
    "append", "arg"
);

void PikaStdData_List_insertMethod(PikaObj *self, Args *_args_){
    int i = args_getInt(_args_, "i");
    Arg* arg = args_getArg(_args_, "arg");
    PikaStdData_List_insert(self, i, arg);
}
method_typedef(
    PikaStdData_List_insert,
    "insert", "i,arg"
);

void PikaStdData_List_popMethod(PikaObj *self, Args *_args_){
    PikaTuple* index = args_getTuple(_args_, "index");
    Arg* res = PikaStdData_List_pop(self, index);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_List_pop,
    "pop", "*index"
);

void PikaStdData_List_removeMethod(PikaObj *self, Args *_args_){
    Arg* val = args_getArg(_args_, "val");
    PikaStdData_List_remove(self, val);
}
method_typedef(
    PikaStdData_List_remove,
    "remove", "val"
);

void PikaStdData_List_reverseMethod(PikaObj *self, Args *_args_){
    PikaStdData_List_reverse(self);
}
method_typedef(
    PikaStdData_List_reverse,
    "reverse", ""
);

void PikaStdData_List_setMethod(PikaObj *self, Args *_args_){
    int i = args_getInt(_args_, "i");
    Arg* arg = args_getArg(_args_, "arg");
    PikaStdData_List_set(self, i, arg);
}
method_typedef(
    PikaStdData_List_set,
    "set", "i,arg"
);

class_def(PikaStdData_List){
    __BEFORE_MOETHOD_DEF
    method_def(PikaStdData_List_insert, 81003162),
    method_def(PikaStdData_List_pop, 193502740),
    method_def(PikaStdData_List_set, 193505681),
    method_def(PikaStdData_List_remove, 422343795),
    method_def(PikaStdData_List___init__, 904762485),
    method_def(PikaStdData_List_reverse, 1062753473),
    method_def(PikaStdData_List___setitem__, **********),
    method_def(PikaStdData_List_append, 1917667549),
    method_def(PikaStdData_List___add__, 2034897290),
    method_def(PikaStdData_List___mul__, 2049747983),
    method_def(PikaStdData_List___str__, 2056834106),
};
class_inhert(PikaStdData_List, PikaStdData_Tuple);

PikaObj *New_PikaStdData_List(Args *args){
    PikaObj *self = New_PikaStdData_Tuple(args);
    obj_setClass(self, PikaStdData_List);
    return self;
}

Arg *PikaStdData_List(PikaObj *self){
    return obj_newObjInPackage(New_PikaStdData_List);
}
#endif

#ifndef PIKA_MODULE_PIKASTDDATA_DISABLE
void PikaStdData_String___getitem__Method(PikaObj *self, Args *_args_){
    Arg* __key = args_getArg(_args_, "__key");
    Arg* res = PikaStdData_String___getitem__(self, __key);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_String___getitem__,
    "__getitem__", "__key"
);

void PikaStdData_String___init__Method(PikaObj *self, Args *_args_){
    char* s = args_getStr(_args_, "s");
    PikaStdData_String___init__(self, s);
}
method_typedef(
    PikaStdData_String___init__,
    "__init__", "s"
);

void PikaStdData_String___iter__Method(PikaObj *self, Args *_args_){
    Arg* res = PikaStdData_String___iter__(self);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_String___iter__,
    "__iter__", ""
);

void PikaStdData_String___len__Method(PikaObj *self, Args *_args_){
    int res = PikaStdData_String___len__(self);
    method_returnInt(_args_, res);
}
method_typedef(
    PikaStdData_String___len__,
    "__len__", ""
);

void PikaStdData_String___next__Method(PikaObj *self, Args *_args_){
    Arg* res = PikaStdData_String___next__(self);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_String___next__,
    "__next__", ""
);

void PikaStdData_String___setitem__Method(PikaObj *self, Args *_args_){
    Arg* __key = args_getArg(_args_, "__key");
    Arg* __val = args_getArg(_args_, "__val");
    PikaStdData_String___setitem__(self, __key, __val);
}
method_typedef(
    PikaStdData_String___setitem__,
    "__setitem__", "__key,__val"
);

void PikaStdData_String___str__Method(PikaObj *self, Args *_args_){
    char* res = PikaStdData_String___str__(self);
    method_returnStr(_args_, res);
}
method_typedef(
    PikaStdData_String___str__,
    "__str__", ""
);

void PikaStdData_String_encodeMethod(PikaObj *self, Args *_args_){
    PikaTuple* encoding = args_getTuple(_args_, "encoding");
    Arg* res = PikaStdData_String_encode(self, encoding);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_String_encode,
    "encode", "*encoding"
);

void PikaStdData_String_endswithMethod(PikaObj *self, Args *_args_){
    char* suffix = args_getStr(_args_, "suffix");
    int res = PikaStdData_String_endswith(self, suffix);
    method_returnInt(_args_, res);
}
method_typedef(
    PikaStdData_String_endswith,
    "endswith", "suffix"
);

void PikaStdData_String_findMethod(PikaObj *self, Args *_args_){
    char* sub = args_getStr(_args_, "sub");
    int res = PikaStdData_String_find(self, sub);
    method_returnInt(_args_, res);
}
method_typedef(
    PikaStdData_String_find,
    "find", "sub"
);

void PikaStdData_String_formatMethod(PikaObj *self, Args *_args_){
    PikaTuple* vars = args_getTuple(_args_, "vars");
    char* res = PikaStdData_String_format(self, vars);
    method_returnStr(_args_, res);
}
method_typedef(
    PikaStdData_String_format,
    "format", "*vars"
);

void PikaStdData_String_getMethod(PikaObj *self, Args *_args_){
    char* res = PikaStdData_String_get(self);
    method_returnStr(_args_, res);
}
method_typedef(
    PikaStdData_String_get,
    "get", ""
);

void PikaStdData_String_isalnumMethod(PikaObj *self, Args *_args_){
    int res = PikaStdData_String_isalnum(self);
    method_returnInt(_args_, res);
}
method_typedef(
    PikaStdData_String_isalnum,
    "isalnum", ""
);

void PikaStdData_String_isalphaMethod(PikaObj *self, Args *_args_){
    int res = PikaStdData_String_isalpha(self);
    method_returnInt(_args_, res);
}
method_typedef(
    PikaStdData_String_isalpha,
    "isalpha", ""
);

void PikaStdData_String_isdigitMethod(PikaObj *self, Args *_args_){
    int res = PikaStdData_String_isdigit(self);
    method_returnInt(_args_, res);
}
method_typedef(
    PikaStdData_String_isdigit,
    "isdigit", ""
);

void PikaStdData_String_islowerMethod(PikaObj *self, Args *_args_){
    int res = PikaStdData_String_islower(self);
    method_returnInt(_args_, res);
}
method_typedef(
    PikaStdData_String_islower,
    "islower", ""
);

void PikaStdData_String_isspaceMethod(PikaObj *self, Args *_args_){
    int res = PikaStdData_String_isspace(self);
    method_returnInt(_args_, res);
}
method_typedef(
    PikaStdData_String_isspace,
    "isspace", ""
);

void PikaStdData_String_joinMethod(PikaObj *self, Args *_args_){
    Arg* val = args_getArg(_args_, "val");
    char* res = PikaStdData_String_join(self, val);
    method_returnStr(_args_, res);
}
method_typedef(
    PikaStdData_String_join,
    "join", "val"
);

void PikaStdData_String_replaceMethod(PikaObj *self, Args *_args_){
    char* old = args_getStr(_args_, "old");
    char* new = args_getStr(_args_, "new");
    char* res = PikaStdData_String_replace(self, old, new);
    method_returnStr(_args_, res);
}
method_typedef(
    PikaStdData_String_replace,
    "replace", "old,new"
);

void PikaStdData_String_setMethod(PikaObj *self, Args *_args_){
    char* s = args_getStr(_args_, "s");
    PikaStdData_String_set(self, s);
}
method_typedef(
    PikaStdData_String_set,
    "set", "s"
);

void PikaStdData_String_splitMethod(PikaObj *self, Args *_args_){
    PikaTuple* s = args_getTuple(_args_, "s");
    PikaObj* res = PikaStdData_String_split(self, s);
    method_returnObj(_args_, res);
}
method_typedef(
    PikaStdData_String_split,
    "split", "*s"
);

void PikaStdData_String_startswithMethod(PikaObj *self, Args *_args_){
    char* prefix = args_getStr(_args_, "prefix");
    int res = PikaStdData_String_startswith(self, prefix);
    method_returnInt(_args_, res);
}
method_typedef(
    PikaStdData_String_startswith,
    "startswith", "prefix"
);

void PikaStdData_String_stripMethod(PikaObj *self, Args *_args_){
    PikaTuple* chrs = args_getTuple(_args_, "chrs");
    char* res = PikaStdData_String_strip(self, chrs);
    method_returnStr(_args_, res);
}
method_typedef(
    PikaStdData_String_strip,
    "strip", "*chrs"
);

class_def(PikaStdData_String){
    __BEFORE_MOETHOD_DEF
    method_def(PikaStdData_String_get, 193492613),
    method_def(PikaStdData_String_set, 193505681),
    method_def(PikaStdData_String_split, 274679281),
    method_def(PikaStdData_String_strip, 274829559),
    method_def(PikaStdData_String_isalnum, 700198430),
    method_def(PikaStdData_String_isalpha, 700200167),
    method_def(PikaStdData_String_isdigit, 703640370),
    method_def(PikaStdData_String_islower, 713360650),
    method_def(PikaStdData_String_isspace, 721673997),
    method_def(PikaStdData_String_startswith, 841709250),
    method_def(PikaStdData_String___init__, 904762485),
    method_def(PikaStdData_String___iter__, 911732085),
    method_def(PikaStdData_String_endswith, 920277419),
    method_def(PikaStdData_String_replace, 1055870465),
    method_def(PikaStdData_String___next__, 1090305216),
    method_def(PikaStdData_String___setitem__, **********),
    method_def(PikaStdData_String___getitem__, 1535436016),
    method_def(PikaStdData_String___len__, 2047989248),
    method_def(PikaStdData_String___str__, 2056834106),
    method_def(PikaStdData_String_encode, 2071380659),
    method_def(PikaStdData_String_find, 2090257254),
    method_def(PikaStdData_String_join, 2090407381),
    method_def(PikaStdData_String_format, 2112238766),
};
class_inhert(PikaStdData_String, TinyObj);

PikaObj *New_PikaStdData_String(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, PikaStdData_String);
    return self;
}

Arg *PikaStdData_String(PikaObj *self){
    return obj_newObjInPackage(New_PikaStdData_String);
}
#endif

#ifndef PIKA_MODULE_PIKASTDDATA_DISABLE
void PikaStdData_Tuple___add__Method(PikaObj *self, Args *_args_){
    PikaObj* others = args_getPtr(_args_, "others");
    PikaObj* res = PikaStdData_Tuple___add__(self, others);
    method_returnObj(_args_, res);
}
method_typedef(
    PikaStdData_Tuple___add__,
    "__add__", "others"
);

void PikaStdData_Tuple___del__Method(PikaObj *self, Args *_args_){
    PikaStdData_Tuple___del__(self);
}
method_typedef(
    PikaStdData_Tuple___del__,
    "__del__", ""
);

void PikaStdData_Tuple___eq__Method(PikaObj *self, Args *_args_){
    Arg* other = args_getArg(_args_, "other");
    int res = PikaStdData_Tuple___eq__(self, other);
    method_returnInt(_args_, res);
}
method_typedef(
    PikaStdData_Tuple___eq__,
    "__eq__", "other"
);

void PikaStdData_Tuple___getitem__Method(PikaObj *self, Args *_args_){
    Arg* __key = args_getArg(_args_, "__key");
    Arg* res = PikaStdData_Tuple___getitem__(self, __key);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_Tuple___getitem__,
    "__getitem__", "__key"
);

void PikaStdData_Tuple___init__Method(PikaObj *self, Args *_args_){
    PikaStdData_Tuple___init__(self);
}
method_typedef(
    PikaStdData_Tuple___init__,
    "__init__", ""
);

void PikaStdData_Tuple___iter__Method(PikaObj *self, Args *_args_){
    Arg* res = PikaStdData_Tuple___iter__(self);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_Tuple___iter__,
    "__iter__", ""
);

void PikaStdData_Tuple___len__Method(PikaObj *self, Args *_args_){
    int res = PikaStdData_Tuple___len__(self);
    method_returnInt(_args_, res);
}
method_typedef(
    PikaStdData_Tuple___len__,
    "__len__", ""
);

void PikaStdData_Tuple___mul__Method(PikaObj *self, Args *_args_){
    int n = args_getInt(_args_, "n");
    PikaObj* res = PikaStdData_Tuple___mul__(self, n);
    method_returnObj(_args_, res);
}
method_typedef(
    PikaStdData_Tuple___mul__,
    "__mul__", "n"
);

void PikaStdData_Tuple___next__Method(PikaObj *self, Args *_args_){
    Arg* res = PikaStdData_Tuple___next__(self);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_Tuple___next__,
    "__next__", ""
);

void PikaStdData_Tuple___str__Method(PikaObj *self, Args *_args_){
    char* res = PikaStdData_Tuple___str__(self);
    method_returnStr(_args_, res);
}
method_typedef(
    PikaStdData_Tuple___str__,
    "__str__", ""
);

void PikaStdData_Tuple_getMethod(PikaObj *self, Args *_args_){
    int i = args_getInt(_args_, "i");
    Arg* res = PikaStdData_Tuple_get(self, i);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_Tuple_get,
    "get", "i"
);

void PikaStdData_Tuple_lenMethod(PikaObj *self, Args *_args_){
    int res = PikaStdData_Tuple_len(self);
    method_returnInt(_args_, res);
}
method_typedef(
    PikaStdData_Tuple_len,
    "len", ""
);

class_def(PikaStdData_Tuple){
    __BEFORE_MOETHOD_DEF
    method_def(PikaStdData_Tuple_get, 193492613),
    method_def(PikaStdData_Tuple_len, 193498052),
    method_def(PikaStdData_Tuple___init__, 904762485),
    method_def(PikaStdData_Tuple___iter__, 911732085),
    method_def(PikaStdData_Tuple___next__, 1090305216),
    method_def(PikaStdData_Tuple___getitem__, 1535436016),
    method_def(PikaStdData_Tuple___eq__, 1818853367),
    method_def(PikaStdData_Tuple___add__, 2034897290),
    method_def(PikaStdData_Tuple___del__, 2038499702),
    method_def(PikaStdData_Tuple___len__, 2047989248),
    method_def(PikaStdData_Tuple___mul__, 2049747983),
    method_def(PikaStdData_Tuple___str__, 2056834106),
};
class_inhert(PikaStdData_Tuple, TinyObj);

PikaObj *New_PikaStdData_Tuple(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, PikaStdData_Tuple);
    return self;
}

Arg *PikaStdData_Tuple(PikaObj *self){
    return obj_newObjInPackage(New_PikaStdData_Tuple);
}
#endif

#ifndef PIKA_MODULE_PIKASTDDATA_DISABLE
void PikaStdData_Utils_int_to_bytesMethod(PikaObj *self, Args *_args_){
    int val = args_getInt(_args_, "val");
    Arg* res = PikaStdData_Utils_int_to_bytes(self, val);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_Utils_int_to_bytes,
    "int_to_bytes", "val"
);

class_def(PikaStdData_Utils){
    __BEFORE_MOETHOD_DEF
    method_def(PikaStdData_Utils_int_to_bytes, 476200216),
};
class_inhert(PikaStdData_Utils, TinyObj);

PikaObj *New_PikaStdData_Utils(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, PikaStdData_Utils);
    return self;
}

Arg *PikaStdData_Utils(PikaObj *self){
    return obj_newObjInPackage(New_PikaStdData_Utils);
}
#endif

#ifndef PIKA_MODULE_PIKASTDDATA_DISABLE
void PikaStdData_dict_items___iter__Method(PikaObj *self, Args *_args_){
    Arg* res = PikaStdData_dict_items___iter__(self);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_dict_items___iter__,
    "__iter__", ""
);

void PikaStdData_dict_items___len__Method(PikaObj *self, Args *_args_){
    int res = PikaStdData_dict_items___len__(self);
    method_returnInt(_args_, res);
}
method_typedef(
    PikaStdData_dict_items___len__,
    "__len__", ""
);

void PikaStdData_dict_items___next__Method(PikaObj *self, Args *_args_){
    Arg* res = PikaStdData_dict_items___next__(self);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_dict_items___next__,
    "__next__", ""
);

void PikaStdData_dict_items___str__Method(PikaObj *self, Args *_args_){
    char* res = PikaStdData_dict_items___str__(self);
    method_returnStr(_args_, res);
}
method_typedef(
    PikaStdData_dict_items___str__,
    "__str__", ""
);

class_def(PikaStdData_dict_items){
    __BEFORE_MOETHOD_DEF
    method_def(PikaStdData_dict_items___iter__, 911732085),
    method_def(PikaStdData_dict_items___next__, 1090305216),
    method_def(PikaStdData_dict_items___len__, 2047989248),
    method_def(PikaStdData_dict_items___str__, 2056834106),
};
class_inhert(PikaStdData_dict_items, TinyObj);

PikaObj *New_PikaStdData_dict_items(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, PikaStdData_dict_items);
    return self;
}

Arg *PikaStdData_dict_items(PikaObj *self){
    return obj_newObjInPackage(New_PikaStdData_dict_items);
}
#endif

#ifndef PIKA_MODULE_PIKASTDDATA_DISABLE
void PikaStdData_dict_keys___iter__Method(PikaObj *self, Args *_args_){
    Arg* res = PikaStdData_dict_keys___iter__(self);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_dict_keys___iter__,
    "__iter__", ""
);

void PikaStdData_dict_keys___len__Method(PikaObj *self, Args *_args_){
    int res = PikaStdData_dict_keys___len__(self);
    method_returnInt(_args_, res);
}
method_typedef(
    PikaStdData_dict_keys___len__,
    "__len__", ""
);

void PikaStdData_dict_keys___next__Method(PikaObj *self, Args *_args_){
    Arg* res = PikaStdData_dict_keys___next__(self);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdData_dict_keys___next__,
    "__next__", ""
);

void PikaStdData_dict_keys___str__Method(PikaObj *self, Args *_args_){
    char* res = PikaStdData_dict_keys___str__(self);
    method_returnStr(_args_, res);
}
method_typedef(
    PikaStdData_dict_keys___str__,
    "__str__", ""
);

class_def(PikaStdData_dict_keys){
    __BEFORE_MOETHOD_DEF
    method_def(PikaStdData_dict_keys___iter__, 911732085),
    method_def(PikaStdData_dict_keys___next__, 1090305216),
    method_def(PikaStdData_dict_keys___len__, 2047989248),
    method_def(PikaStdData_dict_keys___str__, 2056834106),
};
class_inhert(PikaStdData_dict_keys, TinyObj);

PikaObj *New_PikaStdData_dict_keys(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, PikaStdData_dict_keys);
    return self;
}

Arg *PikaStdData_dict_keys(PikaObj *self){
    return obj_newObjInPackage(New_PikaStdData_dict_keys);
}
#endif

#ifndef PIKA_MODULE_PIKASTDLIB_DISABLE
void PikaStdLib_MemCheckerMethod(PikaObj *self, Args *_args_){
    Arg* res = PikaStdLib_MemChecker(self);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdLib_MemChecker,
    "MemChecker", ""
);

void PikaStdLib_REPLMethod(PikaObj *self, Args *_args_){
    Arg* res = PikaStdLib_REPL(self);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdLib_REPL,
    "REPL", ""
);

void PikaStdLib_SysObjMethod(PikaObj *self, Args *_args_){
    Arg* res = PikaStdLib_SysObj(self);
    method_returnArg(_args_, res);
}
method_typedef(
    PikaStdLib_SysObj,
    "SysObj", ""
);

class_def(PikaStdLib){
    __BEFORE_MOETHOD_DEF
    constructor_def(PikaStdLib_MemChecker, 426635353),
    constructor_def(PikaStdLib_SysObj, 1380528799),
    constructor_def(PikaStdLib_REPL, 2089498296),
};
class_inhert(PikaStdLib, TinyObj);

PikaObj *New_PikaStdLib(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, PikaStdLib);
    return self;
}
#endif

#ifndef PIKA_MODULE_PIKASTDLIB_DISABLE
void PikaStdLib_MemChecker_getMaxMethod(PikaObj *self, Args *_args_){
    pika_float res = PikaStdLib_MemChecker_getMax(self);
    method_returnFloat(_args_, res);
}
method_typedef(
    PikaStdLib_MemChecker_getMax,
    "getMax", ""
);

void PikaStdLib_MemChecker_getNowMethod(PikaObj *self, Args *_args_){
    pika_float res = PikaStdLib_MemChecker_getNow(self);
    method_returnFloat(_args_, res);
}
method_typedef(
    PikaStdLib_MemChecker_getNow,
    "getNow", ""
);

void PikaStdLib_MemChecker_maxMethod(PikaObj *self, Args *_args_){
    PikaStdLib_MemChecker_max(self);
}
method_typedef(
    PikaStdLib_MemChecker_max,
    "max", ""
);

void PikaStdLib_MemChecker_nowMethod(PikaObj *self, Args *_args_){
    PikaStdLib_MemChecker_now(self);
}
method_typedef(
    PikaStdLib_MemChecker_now,
    "now", ""
);

void PikaStdLib_MemChecker_resetMaxMethod(PikaObj *self, Args *_args_){
    PikaStdLib_MemChecker_resetMax(self);
}
method_typedef(
    PikaStdLib_MemChecker_resetMax,
    "resetMax", ""
);

class_def(PikaStdLib_MemChecker){
    __BEFORE_MOETHOD_DEF
    method_def(PikaStdLib_MemChecker_max, 193499019),
    method_def(PikaStdLib_MemChecker_now, 193500569),
#if !PIKA_NANO_ENABLE
    method_def(PikaStdLib_MemChecker_resetMax, 593750542),
#endif
#if !PIKA_NANO_ENABLE
    method_def(PikaStdLib_MemChecker_getMax, 2139551979),
#endif
#if !PIKA_NANO_ENABLE
    method_def(PikaStdLib_MemChecker_getNow, 2139553529),
#endif
};
class_inhert(PikaStdLib_MemChecker, TinyObj);

PikaObj *New_PikaStdLib_MemChecker(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, PikaStdLib_MemChecker);
    return self;
}

Arg *PikaStdLib_MemChecker(PikaObj *self){
    return obj_newObjInPackage(New_PikaStdLib_MemChecker);
}
#endif

#ifndef PIKA_MODULE_PIKASTDLIB_DISABLE
void PikaStdLib_REPL_setEchoMethod(PikaObj *self, Args *_args_){
    pika_bool echo = args_getBool(_args_, "echo");
    PikaStdLib_REPL_setEcho(self, echo);
}
method_typedef(
    PikaStdLib_REPL_setEcho,
    "setEcho", "echo"
);

class_def(PikaStdLib_REPL){
    __BEFORE_MOETHOD_DEF
    method_def(PikaStdLib_REPL_setEcho, 203199280),
};
class_inhert(PikaStdLib_REPL, TinyObj);

PikaObj *New_PikaStdLib_REPL(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, PikaStdLib_REPL);
    return self;
}

Arg *PikaStdLib_REPL(PikaObj *self){
    return obj_newObjInPackage(New_PikaStdLib_REPL);
}
#endif

#ifndef PIKA_MODULE_PIKASTDLIB_DISABLE
class_def(PikaStdLib_SysObj){
    __BEFORE_MOETHOD_DEF
};
class_inhert(PikaStdLib_SysObj, TinyObj);

PikaObj *New_PikaStdLib_SysObj(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, PikaStdLib_SysObj);
    return self;
}

Arg *PikaStdLib_SysObj(PikaObj *self){
    return obj_newObjInPackage(New_PikaStdLib_SysObj);
}
#endif

#ifndef PIKA_MODULE_PIKASTDTASK_DISABLE
void PikaStdTask_Task___init__Method(PikaObj *self, Args *_args_){
    PikaStdTask_Task___init__(self);
}
method_typedef(
    PikaStdTask_Task___init__,
    "__init__", ""
);

void PikaStdTask_Task_call_alwaysMethod(PikaObj *self, Args *_args_){
    Arg* fun_todo = args_getArg(_args_, "fun_todo");
    PikaStdTask_Task_call_always(self, fun_todo);
}
method_typedef(
    PikaStdTask_Task_call_always,
    "call_always", "fun_todo"
);

void PikaStdTask_Task_call_period_msMethod(PikaObj *self, Args *_args_){
    Arg* fun_todo = args_getArg(_args_, "fun_todo");
    int period_ms = args_getInt(_args_, "period_ms");
    PikaStdTask_Task_call_period_ms(self, fun_todo, period_ms);
}
method_typedef(
    PikaStdTask_Task_call_period_ms,
    "call_period_ms", "fun_todo,period_ms"
);

void PikaStdTask_Task_call_whenMethod(PikaObj *self, Args *_args_){
    Arg* fun_todo = args_getArg(_args_, "fun_todo");
    Arg* fun_when = args_getArg(_args_, "fun_when");
    PikaStdTask_Task_call_when(self, fun_todo, fun_when);
}
method_typedef(
    PikaStdTask_Task_call_when,
    "call_when", "fun_todo,fun_when"
);

void PikaStdTask_Task_platformGetTickMethod(PikaObj *self, Args *_args_){
    PikaStdTask_Task_platformGetTick(self);
}
method_typedef(
    PikaStdTask_Task_platformGetTick,
    "platformGetTick", ""
);

void PikaStdTask_Task_run_foreverMethod(PikaObj *self, Args *_args_){
    PikaStdTask_Task_run_forever(self);
}
method_typedef(
    PikaStdTask_Task_run_forever,
    "run_forever", ""
);

void PikaStdTask_Task_run_onceMethod(PikaObj *self, Args *_args_){
    PikaStdTask_Task_run_once(self);
}
method_typedef(
    PikaStdTask_Task_run_once,
    "run_once", ""
);

void PikaStdTask_Task_run_until_msMethod(PikaObj *self, Args *_args_){
    int until_ms = args_getInt(_args_, "until_ms");
    PikaStdTask_Task_run_until_ms(self, until_ms);
}
method_typedef(
    PikaStdTask_Task_run_until_ms,
    "run_until_ms", "until_ms"
);

class_def(PikaStdTask_Task){
    __BEFORE_MOETHOD_DEF
    method_def(PikaStdTask_Task_run_forever, 408322738),
    method_def(PikaStdTask_Task_run_until_ms, 854930212),
    method_def(PikaStdTask_Task___init__, 904762485),
    method_def(PikaStdTask_Task_call_always, 1368427953),
    method_def(PikaStdTask_Task_call_period_ms, 1674236450),
    method_def(PikaStdTask_Task_run_once, 1726949022),
    method_def(PikaStdTask_Task_platformGetTick, 1897947957),
    method_def(PikaStdTask_Task_call_when, 2141638002),
};
class_inhert(PikaStdTask_Task, PikaStdLib_SysObj);

PikaObj *New_PikaStdTask_Task(Args *args){
    PikaObj *self = New_PikaStdLib_SysObj(args);
#ifndef PIKA_MODULE_PIKASTDDATA_LIST_DISABLE
    obj_newObj(self, "calls", "PikaStdData_List", New_PikaStdData_List);
#endif
    obj_setClass(self, PikaStdTask_Task);
    return self;
}

Arg *PikaStdTask_Task(PikaObj *self){
    return obj_newObjInPackage(New_PikaStdTask_Task);
}
#endif

#ifndef PIKA_MODULE__JSON_DISABLE
void _json_dumpsMethod(PikaObj *self, Args *_args_){
    Arg* d = args_getArg(_args_, "d");
    char* res = _json_dumps(self, d);
    method_returnStr(_args_, res);
}
method_typedef(
    _json_dumps,
    "dumps", "d"
);

void _json_loadsMethod(PikaObj *self, Args *_args_){
    char* json_str = args_getStr(_args_, "json_str");
    Arg* res = _json_loads(self, json_str);
    method_returnArg(_args_, res);
}
method_typedef(
    _json_loads,
    "loads", "json_str"
);

class_def(_json){
    __BEFORE_MOETHOD_DEF
    method_def(_json_dumps, 257071470),
    method_def(_json_loads, 266329752),
};
class_inhert(_json, TinyObj);

PikaObj *New__json(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, _json);
    return self;
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
void builtins_ArithmeticErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_ArithmeticError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_ArithmeticError,
    "ArithmeticError", ""
);

void builtins_AssertionErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_AssertionError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_AssertionError,
    "AssertionError", ""
);

void builtins_AttributeErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_AttributeError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_AttributeError,
    "AttributeError", ""
);

void builtins_BaseExceptionMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_BaseException(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_BaseException,
    "BaseException", ""
);

void builtins_BlockingIOErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_BlockingIOError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_BlockingIOError,
    "BlockingIOError", ""
);

void builtins_BrokenPipeErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_BrokenPipeError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_BrokenPipeError,
    "BrokenPipeError", ""
);

void builtins_BufferErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_BufferError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_BufferError,
    "BufferError", ""
);

void builtins_BytesWarningMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_BytesWarning(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_BytesWarning,
    "BytesWarning", ""
);

void builtins_ChildProcessErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_ChildProcessError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_ChildProcessError,
    "ChildProcessError", ""
);

void builtins_ConnectionAbortedErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_ConnectionAbortedError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_ConnectionAbortedError,
    "ConnectionAbortedError", ""
);

void builtins_ConnectionErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_ConnectionError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_ConnectionError,
    "ConnectionError", ""
);

void builtins_ConnectionRefusedErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_ConnectionRefusedError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_ConnectionRefusedError,
    "ConnectionRefusedError", ""
);

void builtins_ConnectionResetErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_ConnectionResetError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_ConnectionResetError,
    "ConnectionResetError", ""
);

void builtins_DeprecationWarningMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_DeprecationWarning(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_DeprecationWarning,
    "DeprecationWarning", ""
);

void builtins_EOFErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_EOFError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_EOFError,
    "EOFError", ""
);

void builtins_ExceptionMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_Exception(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_Exception,
    "Exception", ""
);

void builtins_FileExistsErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_FileExistsError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_FileExistsError,
    "FileExistsError", ""
);

void builtins_FileNotFoundErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_FileNotFoundError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_FileNotFoundError,
    "FileNotFoundError", ""
);

void builtins_FloatingPointErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_FloatingPointError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_FloatingPointError,
    "FloatingPointError", ""
);

void builtins_FutureWarningMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_FutureWarning(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_FutureWarning,
    "FutureWarning", ""
);

void builtins_GeneratorExitMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_GeneratorExit(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_GeneratorExit,
    "GeneratorExit", ""
);

void builtins_ImportErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_ImportError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_ImportError,
    "ImportError", ""
);

void builtins_ImportWarningMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_ImportWarning(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_ImportWarning,
    "ImportWarning", ""
);

void builtins_IndentationErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_IndentationError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_IndentationError,
    "IndentationError", ""
);

void builtins_IndexErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_IndexError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_IndexError,
    "IndexError", ""
);

void builtins_InterruptedErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_InterruptedError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_InterruptedError,
    "InterruptedError", ""
);

void builtins_IsADirectoryErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_IsADirectoryError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_IsADirectoryError,
    "IsADirectoryError", ""
);

void builtins_KeyErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_KeyError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_KeyError,
    "KeyError", ""
);

void builtins_KeyboardInterruptMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_KeyboardInterrupt(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_KeyboardInterrupt,
    "KeyboardInterrupt", ""
);

void builtins_LookupErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_LookupError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_LookupError,
    "LookupError", ""
);

void builtins_MemoryErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_MemoryError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_MemoryError,
    "MemoryError", ""
);

void builtins_ModuleNotFoundErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_ModuleNotFoundError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_ModuleNotFoundError,
    "ModuleNotFoundError", ""
);

void builtins_NameErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_NameError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_NameError,
    "NameError", ""
);

void builtins_NotADirectoryErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_NotADirectoryError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_NotADirectoryError,
    "NotADirectoryError", ""
);

void builtins_NotImplementedErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_NotImplementedError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_NotImplementedError,
    "NotImplementedError", ""
);

void builtins_OSErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_OSError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_OSError,
    "OSError", ""
);

void builtins_OverflowErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_OverflowError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_OverflowError,
    "OverflowError", ""
);

void builtins_PendingDeprecationWarningMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_PendingDeprecationWarning(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_PendingDeprecationWarning,
    "PendingDeprecationWarning", ""
);

void builtins_PermissionErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_PermissionError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_PermissionError,
    "PermissionError", ""
);

void builtins_ProcessLookupErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_ProcessLookupError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_ProcessLookupError,
    "ProcessLookupError", ""
);

void builtins_RangeObjMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_RangeObj(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_RangeObj,
    "RangeObj", ""
);

void builtins_RecursionErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_RecursionError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_RecursionError,
    "RecursionError", ""
);

void builtins_ReferenceErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_ReferenceError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_ReferenceError,
    "ReferenceError", ""
);

void builtins_ResourceWarningMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_ResourceWarning(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_ResourceWarning,
    "ResourceWarning", ""
);

void builtins_RuntimeErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_RuntimeError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_RuntimeError,
    "RuntimeError", ""
);

void builtins_RuntimeWarningMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_RuntimeWarning(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_RuntimeWarning,
    "RuntimeWarning", ""
);

void builtins_StopAsyncIterationMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_StopAsyncIteration(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_StopAsyncIteration,
    "StopAsyncIteration", ""
);

void builtins_StopIterationMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_StopIteration(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_StopIteration,
    "StopIteration", ""
);

void builtins_StringObjMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_StringObj(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_StringObj,
    "StringObj", ""
);

void builtins_SyntaxErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_SyntaxError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_SyntaxError,
    "SyntaxError", ""
);

void builtins_SyntaxWarningMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_SyntaxWarning(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_SyntaxWarning,
    "SyntaxWarning", ""
);

void builtins_SystemErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_SystemError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_SystemError,
    "SystemError", ""
);

void builtins_SystemExitMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_SystemExit(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_SystemExit,
    "SystemExit", ""
);

void builtins_TabErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_TabError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_TabError,
    "TabError", ""
);

void builtins_TimeoutErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_TimeoutError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_TimeoutError,
    "TimeoutError", ""
);

void builtins_TypeErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_TypeError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_TypeError,
    "TypeError", ""
);

void builtins_UnboundLocalErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_UnboundLocalError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_UnboundLocalError,
    "UnboundLocalError", ""
);

void builtins_UnicodeDecodeErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_UnicodeDecodeError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_UnicodeDecodeError,
    "UnicodeDecodeError", ""
);

void builtins_UnicodeEncodeErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_UnicodeEncodeError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_UnicodeEncodeError,
    "UnicodeEncodeError", ""
);

void builtins_UnicodeErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_UnicodeError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_UnicodeError,
    "UnicodeError", ""
);

void builtins_UnicodeTranslateErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_UnicodeTranslateError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_UnicodeTranslateError,
    "UnicodeTranslateError", ""
);

void builtins_UnicodeWarningMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_UnicodeWarning(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_UnicodeWarning,
    "UnicodeWarning", ""
);

void builtins_UserWarningMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_UserWarning(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_UserWarning,
    "UserWarning", ""
);

void builtins_ValueErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_ValueError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_ValueError,
    "ValueError", ""
);

void builtins_WarningMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_Warning(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_Warning,
    "Warning", ""
);

void builtins_ZeroDivisionErrorMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_ZeroDivisionError(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_ZeroDivisionError,
    "ZeroDivisionError", ""
);

void builtins___getitem__Method(PikaObj *self, Args *_args_){
    Arg* obj = args_getArg(_args_, "obj");
    Arg* key = args_getArg(_args_, "key");
    Arg* res = builtins___getitem__(self, obj, key);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins___getitem__,
    "__getitem__", "obj,key"
);

void builtins___setitem__Method(PikaObj *self, Args *_args_){
    Arg* obj = args_getArg(_args_, "obj");
    Arg* key = args_getArg(_args_, "key");
    Arg* val = args_getArg(_args_, "val");
    Arg* res = builtins___setitem__(self, obj, key, val);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins___setitem__,
    "__setitem__", "obj,key,val"
);

void builtins_absMethod(PikaObj *self, Args *_args_){
    Arg* val = args_getArg(_args_, "val");
    Arg* res = builtins_abs(self, val);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_abs,
    "abs", "val"
);

void builtins_boolMethod(PikaObj *self, Args *_args_){
    Arg* arg = args_getArg(_args_, "arg");
    pika_bool res = builtins_bool(self, arg);
    method_returnBool(_args_, res);
}
method_typedef(
    builtins_bool,
    "bool", "arg"
);

void builtins_bytearrayMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_bytearray(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_bytearray,
    "bytearray", ""
);

void builtins_bytesMethod(PikaObj *self, Args *_args_){
    Arg* val = args_getArg(_args_, "val");
    Arg* res = builtins_bytes(self, val);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_bytes,
    "bytes", "val"
);

void builtins_cformatMethod(PikaObj *self, Args *_args_){
    char* fmt = args_getStr(_args_, "fmt");
    PikaTuple* var = args_getTuple(_args_, "var");
    char* res = builtins_cformat(self, fmt, var);
    method_returnStr(_args_, res);
}
method_typedef(
    builtins_cformat,
    "cformat", "fmt,*var"
);

void builtins_chrMethod(PikaObj *self, Args *_args_){
    int val = args_getInt(_args_, "val");
    char* res = builtins_chr(self, val);
    method_returnStr(_args_, res);
}
method_typedef(
    builtins_chr,
    "chr", "val"
);

void builtins_clearMethod(PikaObj *self, Args *_args_){
    builtins_clear(self);
}
method_typedef(
    builtins_clear,
    "clear", ""
);

void builtins_dictMethod(PikaObj *self, Args *_args_){
    PikaTuple* val = args_getTuple(_args_, "val");
    Arg* res = builtins_dict(self, val);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_dict,
    "dict", "*val"
);

void builtins_dirMethod(PikaObj *self, Args *_args_){
    Arg* obj = args_getArg(_args_, "obj");
    PikaObj* res = builtins_dir(self, obj);
    method_returnObj(_args_, res);
}
method_typedef(
    builtins_dir,
    "dir", "obj"
);

void builtins_evalMethod(PikaObj *self, Args *_args_){
    char* code = args_getStr(_args_, "code");
    Arg* res = builtins_eval(self, code);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_eval,
    "eval", "code"
);

void builtins_execMethod(PikaObj *self, Args *_args_){
    char* code = args_getStr(_args_, "code");
    builtins_exec(self, code);
}
method_typedef(
    builtins_exec,
    "exec", "code"
);

void builtins_exitMethod(PikaObj *self, Args *_args_){
    builtins_exit(self);
}
method_typedef(
    builtins_exit,
    "exit", ""
);

void builtins_floatMethod(PikaObj *self, Args *_args_){
    Arg* arg = args_getArg(_args_, "arg");
    pika_float res = builtins_float(self, arg);
    method_returnFloat(_args_, res);
}
method_typedef(
    builtins_float,
    "float", "arg"
);

void builtins_gcdumpMethod(PikaObj *self, Args *_args_){
    builtins_gcdump(self);
}
method_typedef(
    builtins_gcdump,
    "gcdump", ""
);

void builtins_getattrMethod(PikaObj *self, Args *_args_){
    PikaObj* obj = args_getPtr(_args_, "obj");
    char* name = args_getStr(_args_, "name");
    Arg* res = builtins_getattr(self, obj, name);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_getattr,
    "getattr", "obj,name"
);

void builtins_hasattrMethod(PikaObj *self, Args *_args_){
    PikaObj* obj = args_getPtr(_args_, "obj");
    char* name = args_getStr(_args_, "name");
    int res = builtins_hasattr(self, obj, name);
    method_returnInt(_args_, res);
}
method_typedef(
    builtins_hasattr,
    "hasattr", "obj,name"
);

void builtins_helpMethod(PikaObj *self, Args *_args_){
    char* name = args_getStr(_args_, "name");
    builtins_help(self, name);
}
method_typedef(
    builtins_help,
    "help", "name"
);

void builtins_hexMethod(PikaObj *self, Args *_args_){
    int val = args_getInt(_args_, "val");
    char* res = builtins_hex(self, val);
    method_returnStr(_args_, res);
}
method_typedef(
    builtins_hex,
    "hex", "val"
);

void builtins_idMethod(PikaObj *self, Args *_args_){
    Arg* obj = args_getArg(_args_, "obj");
    int res = builtins_id(self, obj);
    method_returnInt(_args_, res);
}
method_typedef(
    builtins_id,
    "id", "obj"
);

void builtins_inputMethod(PikaObj *self, Args *_args_){
    PikaTuple* info = args_getTuple(_args_, "info");
    char* res = builtins_input(self, info);
    method_returnStr(_args_, res);
}
method_typedef(
    builtins_input,
    "input", "*info"
);

void builtins_intMethod(PikaObj *self, Args *_args_){
    Arg* arg = args_getArg(_args_, "arg");
    PikaTuple* base = args_getTuple(_args_, "base");
    Arg* res = builtins_int(self, arg, base);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_int,
    "int", "arg,*base"
);

void builtins_isinstanceMethod(PikaObj *self, Args *_args_){
    Arg* object = args_getArg(_args_, "object");
    Arg* classinfo = args_getArg(_args_, "classinfo");
    pika_bool res = builtins_isinstance(self, object, classinfo);
    method_returnBool(_args_, res);
}
method_typedef(
    builtins_isinstance,
    "isinstance", "object,classinfo"
);

void builtins_iterMethod(PikaObj *self, Args *_args_){
    Arg* arg = args_getArg(_args_, "arg");
    Arg* res = builtins_iter(self, arg);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_iter,
    "iter", "arg"
);

void builtins_lenMethod(PikaObj *self, Args *_args_){
    Arg* arg = args_getArg(_args_, "arg");
    int res = builtins_len(self, arg);
    method_returnInt(_args_, res);
}
method_typedef(
    builtins_len,
    "len", "arg"
);

void builtins_listMethod(PikaObj *self, Args *_args_){
    PikaTuple* val = args_getTuple(_args_, "val");
    Arg* res = builtins_list(self, val);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_list,
    "list", "*val"
);

void builtins_maxMethod(PikaObj *self, Args *_args_){
    PikaTuple* val = args_getTuple(_args_, "val");
    Arg* res = builtins_max(self, val);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_max,
    "max", "*val"
);

void builtins_minMethod(PikaObj *self, Args *_args_){
    PikaTuple* val = args_getTuple(_args_, "val");
    Arg* res = builtins_min(self, val);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_min,
    "min", "*val"
);

void builtins_objectMethod(PikaObj *self, Args *_args_){
    Arg* res = builtins_object(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_object,
    "object", ""
);

void builtins_openMethod(PikaObj *self, Args *_args_){
    char* path = args_getStr(_args_, "path");
    char* mode = args_getStr(_args_, "mode");
    PikaObj* res = builtins_open(self, path, mode);
    method_returnObj(_args_, res);
}
method_typedef(
    builtins_open,
    "open", "path,mode"
);

void builtins_ordMethod(PikaObj *self, Args *_args_){
    char* val = args_getStr(_args_, "val");
    int res = builtins_ord(self, val);
    method_returnInt(_args_, res);
}
method_typedef(
    builtins_ord,
    "ord", "val"
);

void builtins_printMethod(PikaObj *self, Args *_args_){
    PikaTuple* val = args_getTuple(_args_, "val");
    PikaDict* ops = args_getDict(_args_, "ops");
    builtins_print(self, val, ops);
}
method_typedef(
    builtins_print,
    "print", "*val,**ops"
);

void builtins_rangeMethod(PikaObj *self, Args *_args_){
    PikaTuple* ax = args_getTuple(_args_, "ax");
    Arg* res = builtins_range(self, ax);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_range,
    "range", "*ax"
);

void builtins_rebootMethod(PikaObj *self, Args *_args_){
    builtins_reboot(self);
}
method_typedef(
    builtins_reboot,
    "reboot", ""
);

void builtins_setattrMethod(PikaObj *self, Args *_args_){
    PikaObj* obj = args_getPtr(_args_, "obj");
    char* name = args_getStr(_args_, "name");
    Arg* val = args_getArg(_args_, "val");
    builtins_setattr(self, obj, name, val);
}
method_typedef(
    builtins_setattr,
    "setattr", "obj,name,val"
);

void builtins_strMethod(PikaObj *self, Args *_args_){
    Arg* arg = args_getArg(_args_, "arg");
    char* res = builtins_str(self, arg);
    method_returnStr(_args_, res);
}
method_typedef(
    builtins_str,
    "str", "arg"
);

void builtins_tupleMethod(PikaObj *self, Args *_args_){
    PikaTuple* val = args_getTuple(_args_, "val");
    Arg* res = builtins_tuple(self, val);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_tuple,
    "tuple", "*val"
);

void builtins_typeMethod(PikaObj *self, Args *_args_){
    Arg* arg = args_getArg(_args_, "arg");
    Arg* res = builtins_type(self, arg);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_type,
    "type", "arg"
);

class_def(builtins){
    __BEFORE_MOETHOD_DEF
    method_def(builtins_id, 5863474),
    constructor_def(builtins_ImportWarning, 11723350),
    constructor_def(builtins_FloatingPointError, 19944365),
    constructor_def(builtins_UnicodeWarning, 83933186),
    constructor_def(builtins_StringObj, 145144695),
    constructor_def(builtins_bytearray, 150321240),
    constructor_def(builtins_SystemExit, 173529092),
    constructor_def(builtins_FileExistsError, 175400911),
    method_def(builtins_abs, 193485979),
    method_def(builtins_chr, 193488354),
    method_def(builtins_dir, 193489476),
    method_def(builtins_hex, 193493706),
    method_def(builtins_int, 193495088),
    method_def(builtins_len, 193498052),
    method_def(builtins_max, 193499019),
    method_def(builtins_min, 193499273),
    method_def(builtins_ord, 193501738),
    method_def(builtins_str, 193506174),
    constructor_def(builtins_OSError, 196451025),
    method_def(builtins_setattr, 204224428),
    constructor_def(builtins_UnicodeError, 247530422),
    method_def(builtins_bytes, 254850636),
    method_def(builtins_clear, 255552908),
    method_def(builtins_float, 259121563),
    method_def(builtins_input, 262752949),
    method_def(builtins_print, 271190290),
    method_def(builtins_range, 272956402),
    method_def(builtins_tuple, 276049327),
    constructor_def(builtins_MemoryError, 282021160),
    constructor_def(builtins_object, 301260540),
    constructor_def(builtins_EOFError, 320116713),
    constructor_def(builtins_ArithmeticError, 351017945),
    constructor_def(builtins_TabError, 366528582),
    constructor_def(builtins_Warning, 391884891),
    method_def(builtins_reboot, 421948272),
    constructor_def(builtins_RuntimeError, 489249939),
    constructor_def(builtins_BufferError, 532402249),
    constructor_def(builtins_StopIteration, 603406554),
    constructor_def(builtins_RecursionError, 650622313),
    constructor_def(builtins_KeyboardInterrupt, 675810883),
    constructor_def(builtins_ConnectionAbortedError, 683722528),
    constructor_def(builtins_PendingDeprecationWarning, 685434382),
    constructor_def(builtins_ChildProcessError, 741086002),
    constructor_def(builtins_NameError, 742300048),
    constructor_def(builtins_AttributeError, 746628451),
    constructor_def(builtins_IsADirectoryError, 758248961),
    constructor_def(builtins_IndexError, 799477063),
    constructor_def(builtins_KeyError, 823649752),
    constructor_def(builtins_UnicodeDecodeError, 838371290),
    method_def(builtins_hasattr, 872734812),
    constructor_def(builtins_BrokenPipeError, 978090846),
    constructor_def(builtins_ZeroDivisionError, 984971860),
    constructor_def(builtins_BlockingIOError, **********),
    constructor_def(builtins_AssertionError, **********),
    constructor_def(builtins_ResourceWarning, **********),
    constructor_def(builtins_LookupError, **********),
    constructor_def(builtins_StopAsyncIteration, **********),
    method_def(builtins_cformat, **********),
    method_def(builtins_isinstance, **********),
    constructor_def(builtins_UserWarning, **********),
    constructor_def(builtins_ModuleNotFoundError, **********),
    constructor_def(builtins_ConnectionRefusedError, **********),
    constructor_def(builtins_BaseException, **********),
    constructor_def(builtins_RuntimeWarning, **********),
    constructor_def(builtins_Exception, **********),
    constructor_def(builtins_UnboundLocalError, **********),
    method_def(builtins___setitem__, **********),
    constructor_def(builtins_FileNotFoundError, **********),
    constructor_def(builtins_PermissionError, 1420080312),
    constructor_def(builtins_SystemError, 1431286868),
    method_def(builtins___getitem__, 1535436016),
    constructor_def(builtins_RangeObj, 1538428845),
    constructor_def(builtins_UnicodeEncodeError, 1538545476),
    constructor_def(builtins_NotImplementedError, 1581723060),
    constructor_def(builtins_ConnectionResetError, 1651090626),
    constructor_def(builtins_SyntaxWarning, 1678827394),
    constructor_def(builtins_InterruptedError, 1696514373),
    constructor_def(builtins_DeprecationWarning, 1745747977),
    constructor_def(builtins_UnicodeTranslateError, 1777242020),
    constructor_def(builtins_IndentationError, 1802197452),
    constructor_def(builtins_ConnectionError, 1818108799),
    constructor_def(builtins_ImportError, 1836878090),
    constructor_def(builtins_FutureWarning, 1879536502),
    method_def(builtins_getattr, 1886477984),
    constructor_def(builtins_ReferenceError, 1905798430),
    constructor_def(builtins_ProcessLookupError, 1911736232),
    constructor_def(builtins_TypeError, 1950608113),
    constructor_def(builtins_SyntaxError, 1956727606),
    constructor_def(builtins_OverflowError, 1985385347),
    constructor_def(builtins_GeneratorExit, 2000609318),
    constructor_def(builtins_NotADirectoryError, 2011759286),
    constructor_def(builtins_TimeoutError, 2075962582),
    method_def(builtins_bool, 2090120081),
    method_def(builtins_dict, 2090185033),
    method_def(builtins_eval, 2090235053),
    method_def(builtins_exec, 2090237354),
    method_def(builtins_exit, 2090237503),
    method_def(builtins_help, 2090324718),
    method_def(builtins_iter, 2090376761),
    method_def(builtins_list, 2090473057),
    method_def(builtins_open, 2090588023),
    method_def(builtins_type, 2090777863),
    constructor_def(builtins_ValueError, 2134842892),
    constructor_def(builtins_BytesWarning, 2135134114),
    method_def(builtins_gcdump, 2136649093),
};
class_inhert(builtins, TinyObj);

PikaObj *New_builtins(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, builtins);
    return self;
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_ArithmeticError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_ArithmeticError, builtins_Exception);

PikaObj *New_builtins_ArithmeticError(Args *args){
    PikaObj *self = New_builtins_Exception(args);
    obj_setClass(self, builtins_ArithmeticError);
    return self;
}

Arg *builtins_ArithmeticError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_ArithmeticError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_AssertionError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_AssertionError, builtins_Exception);

PikaObj *New_builtins_AssertionError(Args *args){
    PikaObj *self = New_builtins_Exception(args);
    obj_setClass(self, builtins_AssertionError);
    return self;
}

Arg *builtins_AssertionError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_AssertionError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_AttributeError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_AttributeError, builtins_Exception);

PikaObj *New_builtins_AttributeError(Args *args){
    PikaObj *self = New_builtins_Exception(args);
    obj_setClass(self, builtins_AttributeError);
    return self;
}

Arg *builtins_AttributeError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_AttributeError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_BaseException){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_BaseException, TinyObj);

PikaObj *New_builtins_BaseException(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, builtins_BaseException);
    return self;
}

Arg *builtins_BaseException(PikaObj *self){
    return obj_newObjInPackage(New_builtins_BaseException);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_BlockingIOError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_BlockingIOError, builtins_OSError);

PikaObj *New_builtins_BlockingIOError(Args *args){
    PikaObj *self = New_builtins_OSError(args);
    obj_setClass(self, builtins_BlockingIOError);
    return self;
}

Arg *builtins_BlockingIOError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_BlockingIOError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_BrokenPipeError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_BrokenPipeError, builtins_ConnectionError);

PikaObj *New_builtins_BrokenPipeError(Args *args){
    PikaObj *self = New_builtins_ConnectionError(args);
    obj_setClass(self, builtins_BrokenPipeError);
    return self;
}

Arg *builtins_BrokenPipeError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_BrokenPipeError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_BufferError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_BufferError, builtins_Exception);

PikaObj *New_builtins_BufferError(Args *args){
    PikaObj *self = New_builtins_Exception(args);
    obj_setClass(self, builtins_BufferError);
    return self;
}

Arg *builtins_BufferError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_BufferError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_BytesWarning){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_BytesWarning, builtins_Warning);

PikaObj *New_builtins_BytesWarning(Args *args){
    PikaObj *self = New_builtins_Warning(args);
    obj_setClass(self, builtins_BytesWarning);
    return self;
}

Arg *builtins_BytesWarning(PikaObj *self){
    return obj_newObjInPackage(New_builtins_BytesWarning);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_ChildProcessError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_ChildProcessError, builtins_OSError);

PikaObj *New_builtins_ChildProcessError(Args *args){
    PikaObj *self = New_builtins_OSError(args);
    obj_setClass(self, builtins_ChildProcessError);
    return self;
}

Arg *builtins_ChildProcessError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_ChildProcessError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_ConnectionAbortedError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_ConnectionAbortedError, builtins_ConnectionError);

PikaObj *New_builtins_ConnectionAbortedError(Args *args){
    PikaObj *self = New_builtins_ConnectionError(args);
    obj_setClass(self, builtins_ConnectionAbortedError);
    return self;
}

Arg *builtins_ConnectionAbortedError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_ConnectionAbortedError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_ConnectionError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_ConnectionError, builtins_OSError);

PikaObj *New_builtins_ConnectionError(Args *args){
    PikaObj *self = New_builtins_OSError(args);
    obj_setClass(self, builtins_ConnectionError);
    return self;
}

Arg *builtins_ConnectionError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_ConnectionError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_ConnectionRefusedError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_ConnectionRefusedError, builtins_ConnectionError);

PikaObj *New_builtins_ConnectionRefusedError(Args *args){
    PikaObj *self = New_builtins_ConnectionError(args);
    obj_setClass(self, builtins_ConnectionRefusedError);
    return self;
}

Arg *builtins_ConnectionRefusedError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_ConnectionRefusedError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_ConnectionResetError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_ConnectionResetError, builtins_ConnectionError);

PikaObj *New_builtins_ConnectionResetError(Args *args){
    PikaObj *self = New_builtins_ConnectionError(args);
    obj_setClass(self, builtins_ConnectionResetError);
    return self;
}

Arg *builtins_ConnectionResetError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_ConnectionResetError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_DeprecationWarning){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_DeprecationWarning, builtins_Warning);

PikaObj *New_builtins_DeprecationWarning(Args *args){
    PikaObj *self = New_builtins_Warning(args);
    obj_setClass(self, builtins_DeprecationWarning);
    return self;
}

Arg *builtins_DeprecationWarning(PikaObj *self){
    return obj_newObjInPackage(New_builtins_DeprecationWarning);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_EOFError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_EOFError, builtins_Exception);

PikaObj *New_builtins_EOFError(Args *args){
    PikaObj *self = New_builtins_Exception(args);
    obj_setClass(self, builtins_EOFError);
    return self;
}

Arg *builtins_EOFError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_EOFError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_Exception){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_Exception, builtins_BaseException);

PikaObj *New_builtins_Exception(Args *args){
    PikaObj *self = New_builtins_BaseException(args);
    obj_setClass(self, builtins_Exception);
    return self;
}

Arg *builtins_Exception(PikaObj *self){
    return obj_newObjInPackage(New_builtins_Exception);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_FileExistsError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_FileExistsError, builtins_OSError);

PikaObj *New_builtins_FileExistsError(Args *args){
    PikaObj *self = New_builtins_OSError(args);
    obj_setClass(self, builtins_FileExistsError);
    return self;
}

Arg *builtins_FileExistsError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_FileExistsError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_FileNotFoundError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_FileNotFoundError, builtins_OSError);

PikaObj *New_builtins_FileNotFoundError(Args *args){
    PikaObj *self = New_builtins_OSError(args);
    obj_setClass(self, builtins_FileNotFoundError);
    return self;
}

Arg *builtins_FileNotFoundError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_FileNotFoundError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_FloatingPointError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_FloatingPointError, builtins_ArithmeticError);

PikaObj *New_builtins_FloatingPointError(Args *args){
    PikaObj *self = New_builtins_ArithmeticError(args);
    obj_setClass(self, builtins_FloatingPointError);
    return self;
}

Arg *builtins_FloatingPointError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_FloatingPointError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_FutureWarning){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_FutureWarning, builtins_Warning);

PikaObj *New_builtins_FutureWarning(Args *args){
    PikaObj *self = New_builtins_Warning(args);
    obj_setClass(self, builtins_FutureWarning);
    return self;
}

Arg *builtins_FutureWarning(PikaObj *self){
    return obj_newObjInPackage(New_builtins_FutureWarning);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_GeneratorExit){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_GeneratorExit, builtins_BaseException);

PikaObj *New_builtins_GeneratorExit(Args *args){
    PikaObj *self = New_builtins_BaseException(args);
    obj_setClass(self, builtins_GeneratorExit);
    return self;
}

Arg *builtins_GeneratorExit(PikaObj *self){
    return obj_newObjInPackage(New_builtins_GeneratorExit);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_ImportError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_ImportError, builtins_Exception);

PikaObj *New_builtins_ImportError(Args *args){
    PikaObj *self = New_builtins_Exception(args);
    obj_setClass(self, builtins_ImportError);
    return self;
}

Arg *builtins_ImportError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_ImportError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_ImportWarning){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_ImportWarning, builtins_Warning);

PikaObj *New_builtins_ImportWarning(Args *args){
    PikaObj *self = New_builtins_Warning(args);
    obj_setClass(self, builtins_ImportWarning);
    return self;
}

Arg *builtins_ImportWarning(PikaObj *self){
    return obj_newObjInPackage(New_builtins_ImportWarning);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_IndentationError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_IndentationError, builtins_SyntaxError);

PikaObj *New_builtins_IndentationError(Args *args){
    PikaObj *self = New_builtins_SyntaxError(args);
    obj_setClass(self, builtins_IndentationError);
    return self;
}

Arg *builtins_IndentationError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_IndentationError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_IndexError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_IndexError, builtins_LookupError);

PikaObj *New_builtins_IndexError(Args *args){
    PikaObj *self = New_builtins_LookupError(args);
    obj_setClass(self, builtins_IndexError);
    return self;
}

Arg *builtins_IndexError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_IndexError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_InterruptedError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_InterruptedError, builtins_OSError);

PikaObj *New_builtins_InterruptedError(Args *args){
    PikaObj *self = New_builtins_OSError(args);
    obj_setClass(self, builtins_InterruptedError);
    return self;
}

Arg *builtins_InterruptedError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_InterruptedError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_IsADirectoryError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_IsADirectoryError, builtins_OSError);

PikaObj *New_builtins_IsADirectoryError(Args *args){
    PikaObj *self = New_builtins_OSError(args);
    obj_setClass(self, builtins_IsADirectoryError);
    return self;
}

Arg *builtins_IsADirectoryError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_IsADirectoryError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_KeyError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_KeyError, builtins_LookupError);

PikaObj *New_builtins_KeyError(Args *args){
    PikaObj *self = New_builtins_LookupError(args);
    obj_setClass(self, builtins_KeyError);
    return self;
}

Arg *builtins_KeyError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_KeyError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_KeyboardInterrupt){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_KeyboardInterrupt, builtins_BaseException);

PikaObj *New_builtins_KeyboardInterrupt(Args *args){
    PikaObj *self = New_builtins_BaseException(args);
    obj_setClass(self, builtins_KeyboardInterrupt);
    return self;
}

Arg *builtins_KeyboardInterrupt(PikaObj *self){
    return obj_newObjInPackage(New_builtins_KeyboardInterrupt);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_LookupError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_LookupError, builtins_Exception);

PikaObj *New_builtins_LookupError(Args *args){
    PikaObj *self = New_builtins_Exception(args);
    obj_setClass(self, builtins_LookupError);
    return self;
}

Arg *builtins_LookupError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_LookupError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_MemoryError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_MemoryError, builtins_Exception);

PikaObj *New_builtins_MemoryError(Args *args){
    PikaObj *self = New_builtins_Exception(args);
    obj_setClass(self, builtins_MemoryError);
    return self;
}

Arg *builtins_MemoryError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_MemoryError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_ModuleNotFoundError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_ModuleNotFoundError, builtins_ImportError);

PikaObj *New_builtins_ModuleNotFoundError(Args *args){
    PikaObj *self = New_builtins_ImportError(args);
    obj_setClass(self, builtins_ModuleNotFoundError);
    return self;
}

Arg *builtins_ModuleNotFoundError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_ModuleNotFoundError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_NameError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_NameError, builtins_Exception);

PikaObj *New_builtins_NameError(Args *args){
    PikaObj *self = New_builtins_Exception(args);
    obj_setClass(self, builtins_NameError);
    return self;
}

Arg *builtins_NameError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_NameError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_NotADirectoryError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_NotADirectoryError, builtins_OSError);

PikaObj *New_builtins_NotADirectoryError(Args *args){
    PikaObj *self = New_builtins_OSError(args);
    obj_setClass(self, builtins_NotADirectoryError);
    return self;
}

Arg *builtins_NotADirectoryError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_NotADirectoryError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_NotImplementedError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_NotImplementedError, builtins_RuntimeError);

PikaObj *New_builtins_NotImplementedError(Args *args){
    PikaObj *self = New_builtins_RuntimeError(args);
    obj_setClass(self, builtins_NotImplementedError);
    return self;
}

Arg *builtins_NotImplementedError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_NotImplementedError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_OSError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_OSError, builtins_Exception);

PikaObj *New_builtins_OSError(Args *args){
    PikaObj *self = New_builtins_Exception(args);
    obj_setClass(self, builtins_OSError);
    return self;
}

Arg *builtins_OSError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_OSError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_OverflowError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_OverflowError, builtins_ArithmeticError);

PikaObj *New_builtins_OverflowError(Args *args){
    PikaObj *self = New_builtins_ArithmeticError(args);
    obj_setClass(self, builtins_OverflowError);
    return self;
}

Arg *builtins_OverflowError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_OverflowError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_PendingDeprecationWarning){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_PendingDeprecationWarning, builtins_Warning);

PikaObj *New_builtins_PendingDeprecationWarning(Args *args){
    PikaObj *self = New_builtins_Warning(args);
    obj_setClass(self, builtins_PendingDeprecationWarning);
    return self;
}

Arg *builtins_PendingDeprecationWarning(PikaObj *self){
    return obj_newObjInPackage(New_builtins_PendingDeprecationWarning);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_PermissionError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_PermissionError, builtins_OSError);

PikaObj *New_builtins_PermissionError(Args *args){
    PikaObj *self = New_builtins_OSError(args);
    obj_setClass(self, builtins_PermissionError);
    return self;
}

Arg *builtins_PermissionError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_PermissionError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_ProcessLookupError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_ProcessLookupError, builtins_OSError);

PikaObj *New_builtins_ProcessLookupError(Args *args){
    PikaObj *self = New_builtins_OSError(args);
    obj_setClass(self, builtins_ProcessLookupError);
    return self;
}

Arg *builtins_ProcessLookupError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_ProcessLookupError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
void builtins_RangeObj___next__Method(PikaObj *self, Args *_args_){
    Arg* res = builtins_RangeObj___next__(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_RangeObj___next__,
    "__next__", ""
);

class_def(builtins_RangeObj){
    __BEFORE_MOETHOD_DEF
    method_def(builtins_RangeObj___next__, 1090305216),
};
class_inhert(builtins_RangeObj, TinyObj);

PikaObj *New_builtins_RangeObj(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, builtins_RangeObj);
    return self;
}

Arg *builtins_RangeObj(PikaObj *self){
    return obj_newObjInPackage(New_builtins_RangeObj);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_RecursionError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_RecursionError, builtins_RuntimeError);

PikaObj *New_builtins_RecursionError(Args *args){
    PikaObj *self = New_builtins_RuntimeError(args);
    obj_setClass(self, builtins_RecursionError);
    return self;
}

Arg *builtins_RecursionError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_RecursionError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_ReferenceError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_ReferenceError, builtins_Exception);

PikaObj *New_builtins_ReferenceError(Args *args){
    PikaObj *self = New_builtins_Exception(args);
    obj_setClass(self, builtins_ReferenceError);
    return self;
}

Arg *builtins_ReferenceError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_ReferenceError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_ResourceWarning){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_ResourceWarning, builtins_Warning);

PikaObj *New_builtins_ResourceWarning(Args *args){
    PikaObj *self = New_builtins_Warning(args);
    obj_setClass(self, builtins_ResourceWarning);
    return self;
}

Arg *builtins_ResourceWarning(PikaObj *self){
    return obj_newObjInPackage(New_builtins_ResourceWarning);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_RuntimeError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_RuntimeError, builtins_Exception);

PikaObj *New_builtins_RuntimeError(Args *args){
    PikaObj *self = New_builtins_Exception(args);
    obj_setClass(self, builtins_RuntimeError);
    return self;
}

Arg *builtins_RuntimeError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_RuntimeError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_RuntimeWarning){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_RuntimeWarning, builtins_Warning);

PikaObj *New_builtins_RuntimeWarning(Args *args){
    PikaObj *self = New_builtins_Warning(args);
    obj_setClass(self, builtins_RuntimeWarning);
    return self;
}

Arg *builtins_RuntimeWarning(PikaObj *self){
    return obj_newObjInPackage(New_builtins_RuntimeWarning);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_StopAsyncIteration){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_StopAsyncIteration, builtins_Exception);

PikaObj *New_builtins_StopAsyncIteration(Args *args){
    PikaObj *self = New_builtins_Exception(args);
    obj_setClass(self, builtins_StopAsyncIteration);
    return self;
}

Arg *builtins_StopAsyncIteration(PikaObj *self){
    return obj_newObjInPackage(New_builtins_StopAsyncIteration);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_StopIteration){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_StopIteration, builtins_Exception);

PikaObj *New_builtins_StopIteration(Args *args){
    PikaObj *self = New_builtins_Exception(args);
    obj_setClass(self, builtins_StopIteration);
    return self;
}

Arg *builtins_StopIteration(PikaObj *self){
    return obj_newObjInPackage(New_builtins_StopIteration);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
void builtins_StringObj___next__Method(PikaObj *self, Args *_args_){
    Arg* res = builtins_StringObj___next__(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_StringObj___next__,
    "__next__", ""
);

class_def(builtins_StringObj){
    __BEFORE_MOETHOD_DEF
    method_def(builtins_StringObj___next__, 1090305216),
};
class_inhert(builtins_StringObj, TinyObj);

PikaObj *New_builtins_StringObj(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, builtins_StringObj);
    return self;
}

Arg *builtins_StringObj(PikaObj *self){
    return obj_newObjInPackage(New_builtins_StringObj);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_SyntaxError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_SyntaxError, builtins_Exception);

PikaObj *New_builtins_SyntaxError(Args *args){
    PikaObj *self = New_builtins_Exception(args);
    obj_setClass(self, builtins_SyntaxError);
    return self;
}

Arg *builtins_SyntaxError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_SyntaxError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_SyntaxWarning){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_SyntaxWarning, builtins_Warning);

PikaObj *New_builtins_SyntaxWarning(Args *args){
    PikaObj *self = New_builtins_Warning(args);
    obj_setClass(self, builtins_SyntaxWarning);
    return self;
}

Arg *builtins_SyntaxWarning(PikaObj *self){
    return obj_newObjInPackage(New_builtins_SyntaxWarning);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_SystemError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_SystemError, builtins_Exception);

PikaObj *New_builtins_SystemError(Args *args){
    PikaObj *self = New_builtins_Exception(args);
    obj_setClass(self, builtins_SystemError);
    return self;
}

Arg *builtins_SystemError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_SystemError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_SystemExit){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_SystemExit, builtins_BaseException);

PikaObj *New_builtins_SystemExit(Args *args){
    PikaObj *self = New_builtins_BaseException(args);
    obj_setClass(self, builtins_SystemExit);
    return self;
}

Arg *builtins_SystemExit(PikaObj *self){
    return obj_newObjInPackage(New_builtins_SystemExit);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_TabError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_TabError, builtins_IndentationError);

PikaObj *New_builtins_TabError(Args *args){
    PikaObj *self = New_builtins_IndentationError(args);
    obj_setClass(self, builtins_TabError);
    return self;
}

Arg *builtins_TabError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_TabError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_TimeoutError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_TimeoutError, builtins_OSError);

PikaObj *New_builtins_TimeoutError(Args *args){
    PikaObj *self = New_builtins_OSError(args);
    obj_setClass(self, builtins_TimeoutError);
    return self;
}

Arg *builtins_TimeoutError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_TimeoutError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_TypeError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_TypeError, builtins_Exception);

PikaObj *New_builtins_TypeError(Args *args){
    PikaObj *self = New_builtins_Exception(args);
    obj_setClass(self, builtins_TypeError);
    return self;
}

Arg *builtins_TypeError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_TypeError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_UnboundLocalError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_UnboundLocalError, builtins_NameError);

PikaObj *New_builtins_UnboundLocalError(Args *args){
    PikaObj *self = New_builtins_NameError(args);
    obj_setClass(self, builtins_UnboundLocalError);
    return self;
}

Arg *builtins_UnboundLocalError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_UnboundLocalError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_UnicodeDecodeError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_UnicodeDecodeError, builtins_UnicodeError);

PikaObj *New_builtins_UnicodeDecodeError(Args *args){
    PikaObj *self = New_builtins_UnicodeError(args);
    obj_setClass(self, builtins_UnicodeDecodeError);
    return self;
}

Arg *builtins_UnicodeDecodeError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_UnicodeDecodeError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_UnicodeEncodeError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_UnicodeEncodeError, builtins_UnicodeError);

PikaObj *New_builtins_UnicodeEncodeError(Args *args){
    PikaObj *self = New_builtins_UnicodeError(args);
    obj_setClass(self, builtins_UnicodeEncodeError);
    return self;
}

Arg *builtins_UnicodeEncodeError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_UnicodeEncodeError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_UnicodeError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_UnicodeError, builtins_ValueError);

PikaObj *New_builtins_UnicodeError(Args *args){
    PikaObj *self = New_builtins_ValueError(args);
    obj_setClass(self, builtins_UnicodeError);
    return self;
}

Arg *builtins_UnicodeError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_UnicodeError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_UnicodeTranslateError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_UnicodeTranslateError, builtins_UnicodeError);

PikaObj *New_builtins_UnicodeTranslateError(Args *args){
    PikaObj *self = New_builtins_UnicodeError(args);
    obj_setClass(self, builtins_UnicodeTranslateError);
    return self;
}

Arg *builtins_UnicodeTranslateError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_UnicodeTranslateError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_UnicodeWarning){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_UnicodeWarning, builtins_Warning);

PikaObj *New_builtins_UnicodeWarning(Args *args){
    PikaObj *self = New_builtins_Warning(args);
    obj_setClass(self, builtins_UnicodeWarning);
    return self;
}

Arg *builtins_UnicodeWarning(PikaObj *self){
    return obj_newObjInPackage(New_builtins_UnicodeWarning);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_UserWarning){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_UserWarning, builtins_Warning);

PikaObj *New_builtins_UserWarning(Args *args){
    PikaObj *self = New_builtins_Warning(args);
    obj_setClass(self, builtins_UserWarning);
    return self;
}

Arg *builtins_UserWarning(PikaObj *self){
    return obj_newObjInPackage(New_builtins_UserWarning);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_ValueError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_ValueError, builtins_Exception);

PikaObj *New_builtins_ValueError(Args *args){
    PikaObj *self = New_builtins_Exception(args);
    obj_setClass(self, builtins_ValueError);
    return self;
}

Arg *builtins_ValueError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_ValueError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_Warning){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_Warning, builtins_Exception);

PikaObj *New_builtins_Warning(Args *args){
    PikaObj *self = New_builtins_Exception(args);
    obj_setClass(self, builtins_Warning);
    return self;
}

Arg *builtins_Warning(PikaObj *self){
    return obj_newObjInPackage(New_builtins_Warning);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_ZeroDivisionError){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_ZeroDivisionError, builtins_ArithmeticError);

PikaObj *New_builtins_ZeroDivisionError(Args *args){
    PikaObj *self = New_builtins_ArithmeticError(args);
    obj_setClass(self, builtins_ZeroDivisionError);
    return self;
}

Arg *builtins_ZeroDivisionError(PikaObj *self){
    return obj_newObjInPackage(New_builtins_ZeroDivisionError);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
void builtins_bytearray___contains__Method(PikaObj *self, Args *_args_){
    Arg* others = args_getArg(_args_, "others");
    int res = builtins_bytearray___contains__(self, others);
    method_returnInt(_args_, res);
}
method_typedef(
    builtins_bytearray___contains__,
    "__contains__", "others"
);

void builtins_bytearray___getitem__Method(PikaObj *self, Args *_args_){
    int __key = args_getInt(_args_, "__key");
    int res = builtins_bytearray___getitem__(self, __key);
    method_returnInt(_args_, res);
}
method_typedef(
    builtins_bytearray___getitem__,
    "__getitem__", "__key"
);

void builtins_bytearray___init__Method(PikaObj *self, Args *_args_){
    Arg* bytes = args_getArg(_args_, "bytes");
    builtins_bytearray___init__(self, bytes);
}
method_typedef(
    builtins_bytearray___init__,
    "__init__", "bytes"
);

void builtins_bytearray___iter__Method(PikaObj *self, Args *_args_){
    Arg* res = builtins_bytearray___iter__(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_bytearray___iter__,
    "__iter__", ""
);

void builtins_bytearray___len__Method(PikaObj *self, Args *_args_){
    int res = builtins_bytearray___len__(self);
    method_returnInt(_args_, res);
}
method_typedef(
    builtins_bytearray___len__,
    "__len__", ""
);

void builtins_bytearray___next__Method(PikaObj *self, Args *_args_){
    Arg* res = builtins_bytearray___next__(self);
    method_returnArg(_args_, res);
}
method_typedef(
    builtins_bytearray___next__,
    "__next__", ""
);

void builtins_bytearray___setitem__Method(PikaObj *self, Args *_args_){
    int __key = args_getInt(_args_, "__key");
    int __val = args_getInt(_args_, "__val");
    builtins_bytearray___setitem__(self, __key, __val);
}
method_typedef(
    builtins_bytearray___setitem__,
    "__setitem__", "__key,__val"
);

void builtins_bytearray___str__Method(PikaObj *self, Args *_args_){
    char* res = builtins_bytearray___str__(self);
    method_returnStr(_args_, res);
}
method_typedef(
    builtins_bytearray___str__,
    "__str__", ""
);

void builtins_bytearray_decodeMethod(PikaObj *self, Args *_args_){
    char* res = builtins_bytearray_decode(self);
    method_returnStr(_args_, res);
}
method_typedef(
    builtins_bytearray_decode,
    "decode", ""
);

void builtins_bytearray_splitMethod(PikaObj *self, Args *_args_){
    PikaTuple* vars = args_getTuple(_args_, "vars");
    PikaObj* res = builtins_bytearray_split(self, vars);
    method_returnObj(_args_, res);
}
method_typedef(
    builtins_bytearray_split,
    "split", "*vars"
);

class_def(builtins_bytearray){
    __BEFORE_MOETHOD_DEF
    method_def(builtins_bytearray_split, 274679281),
    method_def(builtins_bytearray___init__, 904762485),
    method_def(builtins_bytearray___iter__, 911732085),
    method_def(builtins_bytearray___next__, 1090305216),
    method_def(builtins_bytearray___setitem__, **********),
    method_def(builtins_bytearray___getitem__, 1535436016),
    method_def(builtins_bytearray___contains__, 1644201824),
    method_def(builtins_bytearray_decode, 2021571977),
    method_def(builtins_bytearray___len__, 2047989248),
    method_def(builtins_bytearray___str__, 2056834106),
};
class_inhert(builtins_bytearray, TinyObj);

PikaObj *New_builtins_bytearray(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, builtins_bytearray);
    return self;
}

Arg *builtins_bytearray(PikaObj *self){
    return obj_newObjInPackage(New_builtins_bytearray);
}
#endif

#ifndef PIKA_MODULE_BUILTINS_DISABLE
class_def(builtins_object){
    __BEFORE_MOETHOD_DEF
};
class_inhert(builtins_object, TinyObj);

PikaObj *New_builtins_object(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, builtins_object);
    return self;
}

Arg *builtins_object(PikaObj *self){
    return obj_newObjInPackage(New_builtins_object);
}
#endif

#ifndef PIKA_MODULE_CTYPES_DISABLE
class_def(ctypes_c_bool){
    __BEFORE_MOETHOD_DEF
};
class_inhert(ctypes_c_bool, ctypes_c_uint);

PikaObj *New_ctypes_c_bool(Args *args){
    PikaObj *self = New_ctypes_c_uint(args);
    obj_setClass(self, ctypes_c_bool);
    return self;
}

Arg *ctypes_c_bool(PikaObj *self){
    return obj_newObjInPackage(New_ctypes_c_bool);
}
#endif

#ifndef PIKA_MODULE_CTYPES_DISABLE
void ctypes_c_buffer___getitem__Method(PikaObj *self, Args *_args_){
    int __key = args_getInt(_args_, "__key");
    int res = ctypes_c_buffer___getitem__(self, __key);
    method_returnInt(_args_, res);
}
method_typedef(
    ctypes_c_buffer___getitem__,
    "__getitem__", "__key"
);

void ctypes_c_buffer___init__Method(PikaObj *self, Args *_args_){
    Arg* value = args_getArg(_args_, "value");
    int size = args_getInt(_args_, "size");
    ctypes_c_buffer___init__(self, value, size);
}
method_typedef(
    ctypes_c_buffer___init__,
    "__init__", "value,size"
);

class_def(ctypes_c_buffer){
    __BEFORE_MOETHOD_DEF
    method_def(ctypes_c_buffer___init__, 904762485),
    method_def(ctypes_c_buffer___getitem__, 1535436016),
};
class_inhert(ctypes_c_buffer, TinyObj);

PikaObj *New_ctypes_c_buffer(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, ctypes_c_buffer);
    return self;
}

Arg *ctypes_c_buffer(PikaObj *self){
    return obj_newObjInPackage(New_ctypes_c_buffer);
}
#endif

#ifndef PIKA_MODULE_CTYPES_DISABLE
class_def(ctypes_c_byte){
    __BEFORE_MOETHOD_DEF
};
class_inhert(ctypes_c_byte, ctypes_c_uint);

PikaObj *New_ctypes_c_byte(Args *args){
    PikaObj *self = New_ctypes_c_uint(args);
    obj_setClass(self, ctypes_c_byte);
    return self;
}

Arg *ctypes_c_byte(PikaObj *self){
    return obj_newObjInPackage(New_ctypes_c_byte);
}
#endif

#ifndef PIKA_MODULE_CTYPES_DISABLE
class_def(ctypes_c_char){
    __BEFORE_MOETHOD_DEF
};
class_inhert(ctypes_c_char, ctypes_c_wchar_p);

PikaObj *New_ctypes_c_char(Args *args){
    PikaObj *self = New_ctypes_c_wchar_p(args);
    obj_setClass(self, ctypes_c_char);
    return self;
}

Arg *ctypes_c_char(PikaObj *self){
    return obj_newObjInPackage(New_ctypes_c_char);
}
#endif

#ifndef PIKA_MODULE_CTYPES_DISABLE
class_def(ctypes_c_char_p){
    __BEFORE_MOETHOD_DEF
};
class_inhert(ctypes_c_char_p, ctypes_c_wchar_p);

PikaObj *New_ctypes_c_char_p(Args *args){
    PikaObj *self = New_ctypes_c_wchar_p(args);
    obj_setClass(self, ctypes_c_char_p);
    return self;
}

Arg *ctypes_c_char_p(PikaObj *self){
    return obj_newObjInPackage(New_ctypes_c_char_p);
}
#endif

#ifndef PIKA_MODULE_CTYPES_DISABLE
class_def(ctypes_c_double){
    __BEFORE_MOETHOD_DEF
};
class_inhert(ctypes_c_double, ctypes_c_float);

PikaObj *New_ctypes_c_double(Args *args){
    PikaObj *self = New_ctypes_c_float(args);
    obj_setClass(self, ctypes_c_double);
    return self;
}

Arg *ctypes_c_double(PikaObj *self){
    return obj_newObjInPackage(New_ctypes_c_double);
}
#endif

#ifndef PIKA_MODULE_CTYPES_DISABLE
void ctypes_c_float___init__Method(PikaObj *self, Args *_args_){
    pika_float value = args_getFloat(_args_, "value");
    ctypes_c_float___init__(self, value);
}
method_typedef(
    ctypes_c_float___init__,
    "__init__", "value"
);

class_def(ctypes_c_float){
    __BEFORE_MOETHOD_DEF
    method_def(ctypes_c_float___init__, 904762485),
};
class_inhert(ctypes_c_float, TinyObj);

PikaObj *New_ctypes_c_float(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, ctypes_c_float);
    return self;
}

Arg *ctypes_c_float(PikaObj *self){
    return obj_newObjInPackage(New_ctypes_c_float);
}
#endif

#ifndef PIKA_MODULE_CTYPES_DISABLE
class_def(ctypes_c_int){
    __BEFORE_MOETHOD_DEF
};
class_inhert(ctypes_c_int, ctypes_c_uint);

PikaObj *New_ctypes_c_int(Args *args){
    PikaObj *self = New_ctypes_c_uint(args);
    obj_setClass(self, ctypes_c_int);
    return self;
}

Arg *ctypes_c_int(PikaObj *self){
    return obj_newObjInPackage(New_ctypes_c_int);
}
#endif

#ifndef PIKA_MODULE_CTYPES_DISABLE
class_def(ctypes_c_long){
    __BEFORE_MOETHOD_DEF
};
class_inhert(ctypes_c_long, ctypes_c_uint);

PikaObj *New_ctypes_c_long(Args *args){
    PikaObj *self = New_ctypes_c_uint(args);
    obj_setClass(self, ctypes_c_long);
    return self;
}

Arg *ctypes_c_long(PikaObj *self){
    return obj_newObjInPackage(New_ctypes_c_long);
}
#endif

#ifndef PIKA_MODULE_CTYPES_DISABLE
class_def(ctypes_c_longdouble){
    __BEFORE_MOETHOD_DEF
};
class_inhert(ctypes_c_longdouble, ctypes_c_float);

PikaObj *New_ctypes_c_longdouble(Args *args){
    PikaObj *self = New_ctypes_c_float(args);
    obj_setClass(self, ctypes_c_longdouble);
    return self;
}

Arg *ctypes_c_longdouble(PikaObj *self){
    return obj_newObjInPackage(New_ctypes_c_longdouble);
}
#endif

#ifndef PIKA_MODULE_CTYPES_DISABLE
class_def(ctypes_c_longlong){
    __BEFORE_MOETHOD_DEF
};
class_inhert(ctypes_c_longlong, ctypes_c_uint);

PikaObj *New_ctypes_c_longlong(Args *args){
    PikaObj *self = New_ctypes_c_uint(args);
    obj_setClass(self, ctypes_c_longlong);
    return self;
}

Arg *ctypes_c_longlong(PikaObj *self){
    return obj_newObjInPackage(New_ctypes_c_longlong);
}
#endif

#ifndef PIKA_MODULE_CTYPES_DISABLE
class_def(ctypes_c_short){
    __BEFORE_MOETHOD_DEF
};
class_inhert(ctypes_c_short, ctypes_c_uint);

PikaObj *New_ctypes_c_short(Args *args){
    PikaObj *self = New_ctypes_c_uint(args);
    obj_setClass(self, ctypes_c_short);
    return self;
}

Arg *ctypes_c_short(PikaObj *self){
    return obj_newObjInPackage(New_ctypes_c_short);
}
#endif

#ifndef PIKA_MODULE_CTYPES_DISABLE
class_def(ctypes_c_size_t){
    __BEFORE_MOETHOD_DEF
};
class_inhert(ctypes_c_size_t, ctypes_c_uint);

PikaObj *New_ctypes_c_size_t(Args *args){
    PikaObj *self = New_ctypes_c_uint(args);
    obj_setClass(self, ctypes_c_size_t);
    return self;
}

Arg *ctypes_c_size_t(PikaObj *self){
    return obj_newObjInPackage(New_ctypes_c_size_t);
}
#endif

#ifndef PIKA_MODULE_CTYPES_DISABLE
class_def(ctypes_c_ssize_t){
    __BEFORE_MOETHOD_DEF
};
class_inhert(ctypes_c_ssize_t, ctypes_c_uint);

PikaObj *New_ctypes_c_ssize_t(Args *args){
    PikaObj *self = New_ctypes_c_uint(args);
    obj_setClass(self, ctypes_c_ssize_t);
    return self;
}

Arg *ctypes_c_ssize_t(PikaObj *self){
    return obj_newObjInPackage(New_ctypes_c_ssize_t);
}
#endif

#ifndef PIKA_MODULE_CTYPES_DISABLE
class_def(ctypes_c_ubyte){
    __BEFORE_MOETHOD_DEF
};
class_inhert(ctypes_c_ubyte, ctypes_c_uint);

PikaObj *New_ctypes_c_ubyte(Args *args){
    PikaObj *self = New_ctypes_c_uint(args);
    obj_setClass(self, ctypes_c_ubyte);
    return self;
}

Arg *ctypes_c_ubyte(PikaObj *self){
    return obj_newObjInPackage(New_ctypes_c_ubyte);
}
#endif

#ifndef PIKA_MODULE_CTYPES_DISABLE
void ctypes_c_uint___init__Method(PikaObj *self, Args *_args_){
    int value = args_getInt(_args_, "value");
    ctypes_c_uint___init__(self, value);
}
method_typedef(
    ctypes_c_uint___init__,
    "__init__", "value"
);

class_def(ctypes_c_uint){
    __BEFORE_MOETHOD_DEF
    method_def(ctypes_c_uint___init__, 904762485),
};
class_inhert(ctypes_c_uint, TinyObj);

PikaObj *New_ctypes_c_uint(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, ctypes_c_uint);
    return self;
}

Arg *ctypes_c_uint(PikaObj *self){
    return obj_newObjInPackage(New_ctypes_c_uint);
}
#endif

#ifndef PIKA_MODULE_CTYPES_DISABLE
class_def(ctypes_c_ulong){
    __BEFORE_MOETHOD_DEF
};
class_inhert(ctypes_c_ulong, ctypes_c_uint);

PikaObj *New_ctypes_c_ulong(Args *args){
    PikaObj *self = New_ctypes_c_uint(args);
    obj_setClass(self, ctypes_c_ulong);
    return self;
}

Arg *ctypes_c_ulong(PikaObj *self){
    return obj_newObjInPackage(New_ctypes_c_ulong);
}
#endif

#ifndef PIKA_MODULE_CTYPES_DISABLE
class_def(ctypes_c_ulonglong){
    __BEFORE_MOETHOD_DEF
};
class_inhert(ctypes_c_ulonglong, ctypes_c_uint);

PikaObj *New_ctypes_c_ulonglong(Args *args){
    PikaObj *self = New_ctypes_c_uint(args);
    obj_setClass(self, ctypes_c_ulonglong);
    return self;
}

Arg *ctypes_c_ulonglong(PikaObj *self){
    return obj_newObjInPackage(New_ctypes_c_ulonglong);
}
#endif

#ifndef PIKA_MODULE_CTYPES_DISABLE
class_def(ctypes_c_void_p){
    __BEFORE_MOETHOD_DEF
};
class_inhert(ctypes_c_void_p, ctypes_c_uint);

PikaObj *New_ctypes_c_void_p(Args *args){
    PikaObj *self = New_ctypes_c_uint(args);
    obj_setClass(self, ctypes_c_void_p);
    return self;
}

Arg *ctypes_c_void_p(PikaObj *self){
    return obj_newObjInPackage(New_ctypes_c_void_p);
}
#endif

#ifndef PIKA_MODULE_CTYPES_DISABLE
class_def(ctypes_c_wchar){
    __BEFORE_MOETHOD_DEF
};
class_inhert(ctypes_c_wchar, ctypes_c_wchar_p);

PikaObj *New_ctypes_c_wchar(Args *args){
    PikaObj *self = New_ctypes_c_wchar_p(args);
    obj_setClass(self, ctypes_c_wchar);
    return self;
}

Arg *ctypes_c_wchar(PikaObj *self){
    return obj_newObjInPackage(New_ctypes_c_wchar);
}
#endif

#ifndef PIKA_MODULE_CTYPES_DISABLE
void ctypes_c_wchar_p___init__Method(PikaObj *self, Args *_args_){
    char* value = args_getStr(_args_, "value");
    ctypes_c_wchar_p___init__(self, value);
}
method_typedef(
    ctypes_c_wchar_p___init__,
    "__init__", "value"
);

class_def(ctypes_c_wchar_p){
    __BEFORE_MOETHOD_DEF
    method_def(ctypes_c_wchar_p___init__, 904762485),
};
class_inhert(ctypes_c_wchar_p, TinyObj);

PikaObj *New_ctypes_c_wchar_p(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, ctypes_c_wchar_p);
    return self;
}

Arg *ctypes_c_wchar_p(PikaObj *self){
    return obj_newObjInPackage(New_ctypes_c_wchar_p);
}
#endif

#ifndef PIKA_MODULE_CTYPES_DISABLE
void ctypes_create_string_buffer___getitem__Method(PikaObj *self, Args *_args_){
    int __key = args_getInt(_args_, "__key");
    int res = ctypes_create_string_buffer___getitem__(self, __key);
    method_returnInt(_args_, res);
}
method_typedef(
    ctypes_create_string_buffer___getitem__,
    "__getitem__", "__key"
);

void ctypes_create_string_buffer___init__Method(PikaObj *self, Args *_args_){
    int size = args_getInt(_args_, "size");
    ctypes_create_string_buffer___init__(self, size);
}
method_typedef(
    ctypes_create_string_buffer___init__,
    "__init__", "size"
);

class_def(ctypes_create_string_buffer){
    __BEFORE_MOETHOD_DEF
    method_def(ctypes_create_string_buffer___init__, 904762485),
    method_def(ctypes_create_string_buffer___getitem__, 1535436016),
};
class_inhert(ctypes_create_string_buffer, TinyObj);

PikaObj *New_ctypes_create_string_buffer(Args *args){
    PikaObj *self = New_TinyObj(args);
    obj_setClass(self, ctypes_create_string_buffer);
    return self;
}

Arg *ctypes_create_string_buffer(PikaObj *self){
    return obj_newObjInPackage(New_ctypes_create_string_buffer);
}
#endif

