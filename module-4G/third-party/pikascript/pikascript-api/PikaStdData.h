/*
 * [Warning!] This file is auto-generated by pika compiler.
 * Do not edit it manually.
 * The source code is *.pyi file.
 * More details: 
 * English Doc:
 * https://pikadoc-en.readthedocs.io/en/latest/PikaScript%20%E6%A8%A1%E5%9D%97%E6%A6%82%E8%BF%B0.html
 * Chinese Doc:
 * http://pikapython.com/doc/PikaScript%20%E6%A8%A1%E5%9D%97%E6%A6%82%E8%BF%B0.html
 */

#ifndef __PikaStdData__H
#define __PikaStdData__H
#include <stdio.h>
#include <stdlib.h>
#include "PikaObj.h"

PikaObj *New_PikaStdData(Args *args);

Arg* PikaStdData_ByteArray(PikaObj *self);
Arg* PikaStdData_Dict(PikaObj *self);
Arg* PikaStdData_FILEIO(PikaObj *self);
Arg* PikaStdData_List(PikaObj *self);
Arg* PikaStdData_String(PikaObj *self);
Arg* PikaStdData_Tuple(PikaObj *self);
Arg* PikaStdData_Utils(PikaObj *self);
Arg* PikaStdData_dict_items(PikaObj *self);
Arg* PikaStdData_dict_keys(PikaObj *self);

#endif
