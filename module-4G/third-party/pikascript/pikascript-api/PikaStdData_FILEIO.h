/*
 * [Warning!] This file is auto-generated by pika compiler.
 * Do not edit it manually.
 * The source code is *.pyi file.
 * More details: 
 * English Doc:
 * https://pikadoc-en.readthedocs.io/en/latest/PikaScript%20%E6%A8%A1%E5%9D%97%E6%A6%82%E8%BF%B0.html
 * Chinese Doc:
 * http://pikapython.com/doc/PikaScript%20%E6%A8%A1%E5%9D%97%E6%A6%82%E8%BF%B0.html
 */

#ifndef __PikaStdData_FILEIO__H
#define __PikaStdData_FILEIO__H
#include <stdio.h>
#include <stdlib.h>
#include "PikaObj.h"

PikaObj *New_PikaStdData_FILEIO(Args *args);

void PikaStdData_FILEIO_close(PikaObj *self);
int PikaStdData_FILEIO_init(PikaObj *self, char* path, char* mode);
Arg* PikaStdData_FILEIO_read(PikaObj *self, PikaTuple* size);
char* PikaStdData_FILEIO_readline(PikaObj *self);
PikaObj* PikaStdData_FILEIO_readlines(PikaObj *self);
int PikaStdData_FILEIO_seek(PikaObj *self, int offset, PikaTuple* fromwhere);
int PikaStdData_FILEIO_tell(PikaObj *self);
int PikaStdData_FILEIO_write(PikaObj *self, Arg* s);
void PikaStdData_FILEIO_writelines(PikaObj *self, PikaObj* lines);

#endif
