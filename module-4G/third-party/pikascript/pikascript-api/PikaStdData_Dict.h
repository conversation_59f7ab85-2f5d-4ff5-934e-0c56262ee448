/*
 * [Warning!] This file is auto-generated by pika compiler.
 * Do not edit it manually.
 * The source code is *.pyi file.
 * More details: 
 * English Doc:
 * https://pikadoc-en.readthedocs.io/en/latest/PikaScript%20%E6%A8%A1%E5%9D%97%E6%A6%82%E8%BF%B0.html
 * Chinese Doc:
 * http://pikapython.com/doc/PikaScript%20%E6%A8%A1%E5%9D%97%E6%A6%82%E8%BF%B0.html
 */

#ifndef __PikaStdData_Dict__H
#define __PikaStdData_Dict__H
#include <stdio.h>
#include <stdlib.h>
#include "PikaObj.h"

PikaObj *New_PikaStdData_Dict(Args *args);

int PikaStdData_Dict___contains__(PikaObj *self, Arg* val);
void PikaStdData_Dict___del__(PikaObj *self);
int PikaStdData_Dict___eq__(Pika<PERSON>bj *self, Arg* other);
Arg* PikaStdData_Dict___getitem__(PikaObj *self, Arg* __key);
void PikaStdData_Dict___init__(PikaObj *self);
Arg* PikaStdData_Dict___iter__(PikaObj *self);
int PikaStdData_Dict___len__(PikaObj *self);
Arg* PikaStdData_Dict___next__(PikaObj *self);
void PikaStdData_Dict___setitem__(PikaObj *self, Arg* __key, Arg* __val);
char* PikaStdData_Dict___str__(PikaObj *self);
Arg* PikaStdData_Dict_get(PikaObj *self, char* key);
PikaObj* PikaStdData_Dict_items(PikaObj *self);
PikaObj* PikaStdData_Dict_keys(PikaObj *self);
void PikaStdData_Dict_remove(PikaObj *self, char* key);
void PikaStdData_Dict_set(PikaObj *self, char* key, Arg* arg);
void PikaStdData_Dict_update(PikaObj *self, PikaObj* other);

#endif
