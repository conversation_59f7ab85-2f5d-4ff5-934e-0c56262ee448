/*
 * This file is part of the PikaPython project.
 * http://github.com/pikastech/pikapython
 *
 * MIT License
 *
 * Copyright (c) 2021 GorgonMeducer ?? <EMAIL>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
 * THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.
 */

#include "__instruction_template.h"

//! just append ins to the end, insert ins would brake the pre-compiled
//! bytecode.

/* clang-format off */
/* none */
def_ins(NON)
/* get referance */
def_ins(REF)
/* run function */
def_ins(RUN)
/* string */
def_ins(STR)
/* output */
def_ins(OUT)
/* number */
def_ins(NUM)
/* jump */
def_ins(JMP)
/* jump qual zero */
def_ins(JEZ)
/* operator */
def_ins(OPT)
/* define */
def_ins(DEF)
/* return */
def_ins(RET)
/* not equal */
def_ins(NEL)
/* delete */
def_ins(DEL)
/* exist */
def_ins(EST)
/* break */
def_ins(BRK)
/* continue */
def_ins(CTN)
/* global */
def_ins(GLB)
/* run as */
def_ins(RAS)
/* new */
def_ins(NEW)
/* class */
def_ins(CLS)
/* bytes */
def_ins(BYT)
/* list */
def_ins(LST)
/* import */
def_ins(IMP)
/* try */
def_ins(TRY)
/* not try */
def_ins(NTR)
/* raise */
def_ins(RIS)
/* get error code */
def_ins(GER)
/* set error code */
def_ins(SER)
/* dict */
def_ins(DCT)
/* slice */
def_ins(SLC)
/* assert */
def_ins(ASS)
/* expect */
def_ins(EXP)
/* jump no zero */
def_ins(JNZ)
/* tuple */
def_ins(TPL)
/* no list */
def_ins(NLS)
/* inhert */
def_ins(INH)
