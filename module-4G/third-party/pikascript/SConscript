# -*- coding: utf-8 -*-
#====================================================================
#
# Copyright © 2023 China Mobile IOT. All rights reserved.
#
#
#====================================================================


Import('env', 'root_dir')
from ModuleBuild import *

module = 'pikascript'  # 模块名，同簇模块不得重名
current_dir = os.path.join(Dir('.').abspath)
target = None


#========================================================
# ram_source_files：运行在ram中
# flash_source_files：运行在flash中
#========================================================
ram_source_files = [

]

flash_source_files = [
    'pikascript-api/__asset_pikaModules_py_a.c',
    'pikascript-api/__pikaBinding.c',
    'pikascript-api/pikaScript.c',
    'pikascript-core/BaseObj.c',
    'pikascript-core/dataArg.c',
    'pikascript-core/dataArgs.c',
    'pikascript-core/dataLink.c',
    'pikascript-core/dataLinkNode.c',
    'pikascript-core/dataMemory.c',
    'pikascript-core/dataQueue.c',
    'pikascript-core/dataQueueObj.c',
    'pikascript-core/dataStack.c',
    'pikascript-core/dataString.c',
    'pikascript-core/dataStrs.c',
    'pikascript-core/PikaCompiler.c',
    'pikascript-core/PikaObj.c',
    'pikascript-core/PikaParser.c',
    'pikascript-core/PikaPlatform.c',
    'pikascript-core/PikaVM.c',
    'pikascript-core/TinyObj.c',
    'pikascript-lib/PikaStdLib/PikaDebuger_Debuger.c',
    'pikascript-lib/PikaStdLib/PikaStdData_ByteArray.c',
    'pikascript-lib/PikaStdLib/PikaStdData_Dict.c',
    'pikascript-lib/PikaStdLib/PikaStdData_FILEIO.c',
    'pikascript-lib/PikaStdLib/PikaStdData_List.c',
    'pikascript-lib/PikaStdLib/PikaStdData_String.c',
    'pikascript-lib/PikaStdLib/PikaStdData_Tuple.c',
    'pikascript-lib/PikaStdLib/PikaStdData_Utils.c',
    'pikascript-lib/PikaStdLib/PikaStdLib_MemChecker.c',
    'pikascript-lib/PikaStdLib/PikaStdLib_RangeObj.c',
    'pikascript-lib/PikaStdLib/PikaStdLib_StringObj.c',
    'pikascript-lib/PikaStdLib/PikaStdLib_SysObj.c',
    'pikascript-lib/PikaStdLib/PikaStdTask_Task.c',
    'pikascript-lib/ctypes/ctypes_utils.c',
    'pikascript-lib/ctypes/ctypes.c',
    'pikascript-lib/json/_pika_cJSON.c',
    'pikascript-lib/json/_json.c',
]


#========================================================
# public_incs：置于工程环境中，供其他模块引用
# private_incs：仅在本模块中引用
#========================================================
public_incs = [
    
]

private_incs = [
    'pikascript-api',
    'pikascript-core',
    'pikascript-lib/ctypes',
    'pikascript-lib/json',
]


#========================================================
# cpp_define：仅适用于本模块，对其他模块不可见
#========================================================
cpp_defines = [

]


#========================================================
# 模块编译
#========================================================
mod = ModuleBuild(
    env,
    name = module,
    ram_srcs = ram_source_files,
    flash_srcs = flash_source_files,
    pub_incs = public_incs,
    pri_incs = private_incs,
    cpp_define = cpp_defines
)

target = mod.build_object()

Return('target')
