/**
 * @file        cm_rtc.h
 * @brief       RTC管理
 * @copyright   Copyright © 2021 China Mobile IOT. All rights reserved.
 * <AUTHOR> ljw
 * @date        2021/05/28
 *
 * @defgroup rtc rtc
 * @ingroup SYS
 * @{
 */

#ifndef __CM_RTC_H__
#define __CM_RTC_H__

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <stdint.h>
#include <stdbool.h>
#include <time.h>

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/


/****************************************************************************
 * Public Types
 ****************************************************************************/


/****************************************************************************
 * Public Data
 ****************************************************************************/


/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/

#ifdef __cplusplus
#define EXTERN extern "C"
extern "C"
{
#else
#define EXTERN extern
#endif


typedef struct {
    int32_t tm_sec;     /*!< 秒 – 取值区间为[0,59] */
    int32_t tm_min;     /*!< 分 - 取值区间为[0,59] */
    int32_t tm_hour;    /*!< 时 - 取值区间为[0,23] */
    int32_t tm_mday;    /*!< 日期 - 取值区间为[1,31] */
    int32_t tm_mon;     /*!< 月份 - 取值区间为[1,12] */
    int32_t tm_year;    /*!< 年份 */
} cm_tm_t;

typedef void (*cm_rtc_alarm_cb)(void);


/**
 * @brief 设置系统时间（秒）
 * 
 * @param [in] second 时间戳(单位：秒)
  *
 * @return 
 *   = 0  - 成功 \n
 *   < 0  - 失败, 返回值为错误码
 *  
 * @details UTC时间
 */
int32_t cm_rtc_set_current_time(uint64_t second);

/**
 * @brief 获取系统时间（秒）
 *
 * @return 时间戳（单位：秒）
 *  
 * @details UTC时间
 */
uint64_t cm_rtc_get_current_time(void);

/**
 * @brief 设置时区
 * 
 * @param [in] timezone 时区
 *
 * @return 
 *   = 0  - 成功 \n
 *   < 0  - 失败, 返回值为错误码
 *
 * @details timezone设置范围为-12~12，-12为西十二区，0为世界时间，12为东十二区
 */
int32_t cm_rtc_set_timezone(int32_t timezone);

/**
 * @brief 获取时区
 *
 * @return 时区
 *  
 * @details timezone范围为-12~12，-12为西十二区，0为世界时间，12为东十二区
 */
int32_t cm_rtc_get_timezone(void);

/**
 * @brief 注册闹钟回调函数，闹钟定时到达时触发
 * 
 * @param [in] cb 用户回调函数
 *
 * @return None
 *
 * @details 禁止在中断回调中做阻塞业务
 */
void cm_rtc_register_alarm_cb(cm_rtc_alarm_cb cb);

/**
 * @brief 使能/失能闹钟
 * 
 * @param [in] on_off true：使能，false：失能 
 *
 * @return None
 *
 * @details 
 */
void cm_rtc_enable_alarm(bool on_off);

/**
 * @brief 设置闹钟
 * 
 * @param [in] a_time 时间
 *
 * @return  
 *   = 0  - 成功 \n
 *   < 0  - 失败, 返回值为错误码
 *
 * @details 
 */
int32_t cm_rtc_set_alarm(cm_tm_t *a_time);

/**
 * @brief 读取闹钟
 * 
 * @param [in] a_time 时间
 *
 * @return  
 *   = 0  - 成功 \n
 *   < 0  - 失败, 返回值为错误码
 *
 * @details 
 */
int32_t cm_rtc_get_alarm(cm_tm_t *a_time);


#undef EXTERN
#ifdef __cplusplus
}
#endif

#endif /* __CM_RTC_H__ */

/** @}*/
